namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.UpdateOrganizationalNode;

/// <summary>
/// Validator for UpdateOrganizationalNodeCommand
/// </summary>
public sealed class UpdateOrganizationalNodeCommandValidator : AbstractValidator<UpdateOrganizationalNodeCommand>
{
    public UpdateOrganizationalNodeCommandValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Code is required.")
            .Must(OrganizationalCodeHelper.IsValidCode)
            .WithMessage("Code must be exactly 5 characters using Base-36 format (0-9, A-Z).");


        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required.")
            .MaximumLength(200)
            .WithMessage("Name cannot exceed 200 characters.")
            .Matches(@"^[\w\s\-\.]+$")
            .WithMessage("Name can only contain letters, numbers, spaces, hyphens, and periods.");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters.")
            .When(x => !string.IsNullOrEmpty(x.Description));
    }
}