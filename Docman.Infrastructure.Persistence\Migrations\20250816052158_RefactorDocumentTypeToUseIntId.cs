﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Docman.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RefactorDocumentTypeToUseIntId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_DocumentTypes",
                table: "DocumentTypes");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "DocumentTypes",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                comment: "Name of the document type (must be unique)",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldComment: "Unique name of the document type (Primary Key)");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "DocumentTypes",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "Unique identifier of the document type (Primary Key)")
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_DocumentTypes",
                table: "DocumentTypes",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_DocumentTypes_Name",
                table: "DocumentTypes",
                column: "Name",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_DocumentTypes",
                table: "DocumentTypes");

            migrationBuilder.DropIndex(
                name: "IX_DocumentTypes_Name",
                table: "DocumentTypes");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "DocumentTypes");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "DocumentTypes",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                comment: "Unique name of the document type (Primary Key)",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldComment: "Name of the document type (must be unique)");

            migrationBuilder.AddPrimaryKey(
                name: "PK_DocumentTypes",
                table: "DocumentTypes",
                column: "Name");
        }
    }
}
