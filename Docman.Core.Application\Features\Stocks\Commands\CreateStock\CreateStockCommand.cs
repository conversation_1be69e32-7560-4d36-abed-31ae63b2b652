using Docman.Core.Application.Common.DTOs.Stocks;

namespace Docman.Core.Application.Features.Stocks.Commands.CreateStock;

/// <summary>
/// Command to create a new stock
/// </summary>
public sealed record CreateStockCommand(
    string Name,
    string ManagerName,
    string VillageCode,
    string Address,
    string? MaintenancePhone,
    string? SecurityPhone,
    string? Email,
    string? Description,
    ICollection<CreateStockPhoneRequest> StockPhones
) : IRequest<Result<StockDetailsDto>>;
