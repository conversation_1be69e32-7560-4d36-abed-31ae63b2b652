﻿namespace Docman.Infrastructure.Persistence.Configurations;

public class GovernorateConfiguration : IEntityTypeConfiguration<Governorate>
{
    public void Configure(EntityTypeBuilder<Governorate> builder)
    {
        builder.HasKey(g => g.Code);

        builder.Property(g => g.Code)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(g => g.NameAr)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(g => g.NameEn)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(g => g.CountryCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.HasMany(g => g.CityOrDistricts)
            .WithOne(cd => cd.Governorate)
            .HasForeignKey(cd => cd.GovernorateCode)
            .OnDelete(DeleteBehavior.Cascade);
    }
}