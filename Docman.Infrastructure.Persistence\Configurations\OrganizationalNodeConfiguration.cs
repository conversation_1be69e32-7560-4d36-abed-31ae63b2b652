namespace Docman.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for OrganizationalNode entity
/// </summary>
public class OrganizationalNodeConfiguration : IEntityTypeConfiguration<OrganizationalNode>
{
    public void Configure(EntityTypeBuilder<OrganizationalNode> builder)
    {
        // Table configuration
        builder.ToTable("OrganizationalNodes");

        // Primary key
        builder.HasKey(x => x.Code);

        // Properties
        builder.Property(x => x.Code)
            .HasMaxLength(5)
            .IsRequired();

        builder.Property(x => x.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(x => x.Description)
            .HasMaxLength(1000);

        builder.Property(x => x.Level)
            .HasMaxLength(2)
            .IsRequired();

        builder.Property(x => x.ParentCode)
            .HasMaxLength(5);

        // Relationships
        builder.HasOne(x => x.Parent)
            .WithMany(x => x.Children)
            .HasForeignKey(x => x.ParentCode)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_OrganizationalNodes_Parent");

        
        // Value converters for enum
        builder.Property(x => x.Level)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<OrganizationalLevel>(v));
    }
}
