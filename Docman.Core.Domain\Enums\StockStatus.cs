namespace Docman.Core.Domain.Enums;

/// <summary>
/// Represents the different status values for a stock
/// </summary>
public enum StockStatus
{
    /// <summary>
    /// Stock is newly added or modified and awaiting approval
    /// </summary>
    Unapproved = 0,

    /// <summary>
    /// Stock is confirmed and accepted
    /// </summary>
    Approved = 1,

    /// <summary>
    /// Stock is inactive or frozen
    /// </summary>
    Frozen = 2
}
