using System.Reflection;
using Docman.Core.Application.Common.Authentication;
using Docman.Core.Application.Common.Options;
using Docman.Infrastructure.Persistence.Authentication;
using Docman.Infrastructure.Persistence.Authentication.Filters;
using Docman.Infrastructure.Persistence.Repositories;
using Docman.Infrastructure.Persistence.Repositories.Specifications;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;

namespace Docman.Infrastructure.Persistence;

public static class DependencyInjection
{
    public static IServiceCollection AddPersistenceDependencies(this IServiceCollection services,
        IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection")
                               ?? throw new InvalidOperationException(
                                   "Connection string 'DefaultConnection' not found.");

        services.AddDbContext<DocmanDbContext>(options =>
        {
            options.UseSqlServer(connectionString,
                b => { b.MigrationsAssembly(Assembly.GetExecutingAssembly().FullName); });
        });
        services.AddScoped<IAuthRepository, AuthRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
        services.AddScoped<IOrganizationalNodeRepository, OrganizationalNodeRepository>();

        // Add Identity configurations
        AddIdentityConfigurations(services,configuration);

        return services;
    }

    private static IServiceCollection AddIdentityConfigurations(IServiceCollection services , IConfiguration configuration)
    {
        services.AddSingleton<IJwtProvider, JwtProvider>();

        services.AddIdentity<ApplicationUser, ApplicationRole>()
        .AddEntityFrameworkStores<DocmanDbContext>()
            .AddDefaultTokenProviders();

        services.AddTransient<IAuthorizationHandler, PermissionAuthorizationHandler>();
        services.AddTransient<IAuthorizationPolicyProvider, PermissionAuthrizationPolicyProvider>();

        services.AddOptions<JwtOptions>()
               .BindConfiguration(JwtOptions.SectionName)
               .ValidateDataAnnotations()
               .ValidateOnStart();

        var jwtSettings = configuration.GetSection(JwtOptions.SectionName).Get<JwtOptions>();

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(o =>
        {
            o.SaveToken = true;
            o.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings?.Key!)),
                ValidIssuer = jwtSettings?.Issuer,
                ValidAudience = jwtSettings?.Audience
            };
        });

        services.Configure<IdentityOptions>(options =>
        {
            options.Password.RequiredLength = 8;
           // options.SignIn.RequireConfirmedEmail = true;
            options.User.RequireUniqueEmail = true;

        });


        return services;
    }
}