using System.Diagnostics;
using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.EntityFrameworkCore;

namespace Docman.Presentation.API.Exceptions;

public class GlobalExceptionHandler(
    ILogger<GlobalExceptionHandler> logger,
    IHostEnvironment environment) : IExceptionHandler
{
    private readonly JsonSerializerOptions _serializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
    };

    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        var traceId = Activity.Current?.Id ?? httpContext.TraceIdentifier;
        
        logger.LogError(exception, "Unhandled exception occurred. TraceId: {TraceId}, RequestPath: {Path}",
            traceId, httpContext.Request.Path);

        var statusCode = GetStatusCode(exception);

        var problemDetails = CreateProblemDetails(exception, statusCode, traceId, httpContext);

        httpContext.Response.Clear();
        httpContext.Response.StatusCode = statusCode;
        httpContext.Response.ContentType = "application/json";

        await httpContext.Response.WriteAsJsonAsync(problemDetails, _serializerOptions, cancellationToken);
        return true;
    }

    private static int GetStatusCode(Exception exception) => exception switch
    {
        // System-level exceptions only (business logic errors are now handled via Result pattern)
        BadHttpRequestException or ArgumentException => StatusCodes.Status400BadRequest,
        UnauthorizedAccessException => StatusCodes.Status401Unauthorized,
        KeyNotFoundException => StatusCodes.Status404NotFound,
        DbUpdateConcurrencyException => StatusCodes.Status409Conflict,
        DbUpdateException => StatusCodes.Status500InternalServerError,
        OperationCanceledException => StatusCodes.Status503ServiceUnavailable,
        NotImplementedException => StatusCodes.Status501NotImplemented,
        
        _ => StatusCodes.Status500InternalServerError
    };
    
    private ProblemDetails CreateProblemDetails(Exception exception, int statusCode, string traceId,
        HttpContext httpContext)
    {
        var problemDetails = new ProblemDetails
        {
            Status = statusCode,
            Title = GetTitle(exception),
            Type = $"https://tools.ietf.org/html/rfc7231#{statusCode}",
            Detail = GetDetail(exception),
            Instance = httpContext.Request.Path,
            Extensions =
            {
                ["traceId"] = traceId,
                ["requestId"] = httpContext.Connection.Id,
                ["timestamp"] = DateTimeOffset.UtcNow.ToString("O")
            }
        };

        return problemDetails;
    }

    private string GetDetail(Exception exception)
    {
        return exception switch
        {
            _ when environment.IsDevelopment() => exception.ToString(),
            _ => exception.Message
        };
    }

    private static string GetTitle(Exception exception) => exception switch
    {
        // System-level exceptions
        BadHttpRequestException or ArgumentException => "Invalid Request",
        UnauthorizedAccessException => "Unauthorized",
        KeyNotFoundException => "Resource Not Found",
        DbUpdateConcurrencyException => "Concurrency Conflict",
        DbUpdateException => "Database Error",
        OperationCanceledException => "Service Unavailable",
        NotImplementedException => "Not Implemented",
        
        _ => "Internal Server Error"
    };
}
