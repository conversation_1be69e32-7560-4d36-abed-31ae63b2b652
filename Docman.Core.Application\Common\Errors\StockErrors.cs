namespace Docman.Core.Application.Common.Errors;

/// <summary>
/// Contains error definitions specific to Stock operations
/// </summary>
public static class StockErrors
{
    public static readonly Error StockNotFound = new("Stock.NotFound", "The specified stock was not found.");
    
    public static readonly Error VillageNotFound = new("Stock.VillageNotFound", "The specified village was not found.");
    
    public static readonly Error InvalidStockData = new("Stock.InvalidData", "The provided stock data is invalid.");
    
    public static readonly Error StockAlreadyExists = new("Stock.AlreadyExists", "A stock with the same name already exists in this location.");
    
    public static readonly Error InvalidStatusTransition = new("Stock.InvalidStatusTransition", "The requested status transition is not allowed.");
    
    public static readonly Error StockPhoneNotFound = new("Stock.PhoneNotFound", "The specified stock phone was not found.");
    
    public static readonly Error InvalidPhoneNumber = new("Stock.InvalidPhoneNumber", "The provided phone number is invalid.");
    
    public static readonly Error DuplicatePhoneNumber = new("Stock.DuplicatePhoneNumber", "A phone number with the same value already exists for this stock.");
}
