// Global using statements for WebAPI layer
global using System;
global using System.Collections.Generic;
global using System.Threading;
global using System.Threading.Tasks;
global using Microsoft.AspNetCore.Mvc;
global using Docman.Presentation.API.Controllers.Base;
global using MediatR;
global using Docman.Core.Application.Common.Results;
global using Docman.Core.Domain.Enums;
global using Docman.Presentation.API.Common;
global using Microsoft.AspNetCore.Authorization;
global using Swashbuckle.AspNetCore.Annotations;
