using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Storage;

namespace Docman.Core.Application.Contracts;

/// <summary>
/// Generic repository interface for all entity types
/// </summary>
/// <typeparam name="T">Entity type</typeparam>
public interface IGenericRepository<T> : IDisposable where T : class
{
    // Query methods
    Task<List<T>> GetAllAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default);

    Task<T?> GetAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default);

    Task<IQueryable<T>> GetForPaginationAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false);

    Task<bool> AnyAsync(
        Expression<Func<T, bool>> filter, 
        CancellationToken cancellationToken = default);

    // Command methods
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task UpdateAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);

    // Transaction methods
    Task<IDbContextTransaction> BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}