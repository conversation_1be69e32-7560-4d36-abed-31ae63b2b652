using Docman.Core.Application.Common.DTOs.Buildings;


namespace Docman.Core.Application.Features.Buildings.Queries.GetAllBuildings;

/// <summary>
/// Handler for GetAllBuildingsQuery
/// </summary>
public sealed class GetAllBuildingsQueryHandler(
    IGenericRepository<Building> buildingRepository
) : IRequestHandler<GetAllBuildingsQuery, Result<IEnumerable<BuildingListDto>>>
{
    public async Task<Result<IEnumerable<BuildingListDto>>> Handle(GetAllBuildingsQuery request, CancellationToken cancellationToken)
    {
        // Get all buildings with related location data
        var buildings = await buildingRepository.GetAllAsync(
            includeProperties: ["DistrictOrVillage.CityOrDistrict.Governorate.Country"],
            cancellationToken: cancellationToken
        );

        // Map to DTOs using Mapster
        var buildingDtos = buildings
            .Where(b => !b.IsDeleted) // Filter out soft-deleted buildings
            .OrderBy(b => b.Name)
            .Adapt<List<BuildingListDto>>();

        return Result.Success<IEnumerable<BuildingListDto>>(buildingDtos);
    }
}
