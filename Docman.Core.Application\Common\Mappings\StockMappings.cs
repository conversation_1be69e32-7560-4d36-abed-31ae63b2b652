using Docman.Core.Application.Common.DTOs.Stocks;

namespace Docman.Core.Application.Common.Mappings;

/// <summary>
/// Mapping configurations for Stock-related entities and DTOs using Mapster
/// </summary>
public class StockMappings : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // Stock to StockListDto mapping
        config.NewConfig<Stock, StockListDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.ManagerName, src => src.ManagerName)
            .Map(dest => dest.Status, src => src.Status)
            .Map(dest => dest.VillageNameAr, src => src.Village.NameAr)
            .Map(dest => dest.CityOrDistrictNameAr, src => src.Village.CityOrDistrict.NameAr)
            .Map(dest => dest.CityOrDistrictNameEn, src => src.Village.CityOrDistrict.NameEn)
            .Map(dest => dest.GovernorateNameAr, src => src.Village.CityOrDistrict.Governorate.NameAr)
            .Map(dest => dest.GovernorateNameEn, src => src.Village.CityOrDistrict.Governorate.NameEn)
            .Map(dest => dest.CountryNameAr, src => src.Village.CityOrDistrict.Governorate.Country.NameAr)
            .Map(dest => dest.CountryNameEn, src => src.Village.CityOrDistrict.Governorate.Country.NameEn);

        // Stock to StockDetailsDto mapping
        config.NewConfig<Stock, StockDetailsDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.ManagerName, src => src.ManagerName)
            .Map(dest => dest.VillageCode, src => src.VillageCode)
            .Map(dest => dest.Address, src => src.Address)
            .Map(dest => dest.MaintenancePhone, src => src.MaintenancePhone)
            .Map(dest => dest.SecurityPhone, src => src.SecurityPhone)
            .Map(dest => dest.Email, src => src.Email)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.Status, src => src.Status)
            // Location details
            .Map(dest => dest.VillageNameAr, src => src.Village.NameAr)
            .Map(dest => dest.CityOrDistrictNameAr, src => src.Village.CityOrDistrict.NameAr)
            .Map(dest => dest.CityOrDistrictNameEn, src => src.Village.CityOrDistrict.NameEn)
            .Map(dest => dest.GovernorateNameAr, src => src.Village.CityOrDistrict.Governorate.NameAr)
            .Map(dest => dest.GovernorateNameEn, src => src.Village.CityOrDistrict.Governorate.NameEn)
            .Map(dest => dest.CountryNameAr, src => src.Village.CityOrDistrict.Governorate.Country.NameAr)
            .Map(dest => dest.CountryNameEn, src => src.Village.CityOrDistrict.Governorate.Country.NameEn)
            // Stock phones
            .Map(dest => dest.StockPhones, src => src.StockPhones)
            // Audit properties
            .Map(dest => dest.CreatedByID, src => src.CreatedByID)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.UpdatedByID, src => src.UpdatedByID)
            .Map(dest => dest.UpdatedAt, src => src.UpdatedAt);

        // StockPhone to StockPhoneDto mapping
        config.NewConfig<StockPhone, StockPhoneDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.PhoneNumber, src => src.PhoneNumber)
            .Map(dest => dest.Notes, src => src.Notes);

        // CreateStockRequest to Stock mapping
        config.NewConfig<CreateStockRequest, Stock>()
            .Map(dest => dest.Name, src => src.Name.Trim())
            .Map(dest => dest.ManagerName, src => src.ManagerName.Trim())
            .Map(dest => dest.VillageCode, src => src.VillageCode.Trim())
            .Map(dest => dest.Address, src => src.Address.Trim())
            .Map(dest => dest.MaintenancePhone, src => src.MaintenancePhone != null ? src.MaintenancePhone.Trim() : null)
            .Map(dest => dest.SecurityPhone, src => src.SecurityPhone != null ? src.SecurityPhone.Trim() : null)
            .Map(dest => dest.Email, src => src.Email != null ? src.Email.Trim() : null)
            .Map(dest => dest.Description, src => src.Description != null ? src.Description.Trim() : null)
            .Map(dest => dest.Status, src => StockStatus.Unapproved) // Always default to Unapproved
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.Village)
            .Ignore(dest => dest.StockPhones)
            .Ignore(dest => dest.CreatedByID)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.UpdatedByID)
            .Ignore(dest => dest.UpdatedAt)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.DeletedAt)
            .Ignore(dest => dest.DeletedByID)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.UpdatedBy)
            .Ignore(dest => dest.DeletedBy);

        // CreateStockPhoneRequest to StockPhone mapping
        config.NewConfig<CreateStockPhoneRequest, StockPhone>()
            .Map(dest => dest.PhoneNumber, src => src.PhoneNumber.Trim())
            .Map(dest => dest.Notes, src => src.Notes != null ? src.Notes.Trim() : null)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.StockId)
            .Ignore(dest => dest.Stock);
    }
}
