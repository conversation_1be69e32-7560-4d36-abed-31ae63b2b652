# Docman API - Clean Architecture Documentation

## Table of Contents

- [Project Structure](#project-structure)
- [Layer Responsibilities](#layer-responsibilities)
- [Naming Conventions](#naming-conventions)
- [Namespace & File Scope](#namespace--file-scope)

---

## Project Structure

### Layer Dependencies

![Alt text](Layer_Dependencies.png)

### Folder Hierarchy

```
Docman.sln
├── Core/                                    # Core business layer folder
│   ├── Docman.Core.Domain/                 # Domain layer
│   │   ├── Common/
│   │   │   └── BaseEntity.cs               # Base entity with audit properties
│   │   ├── Entities/
│   │   │   └── User.cs                     # Domain entities
│   │   └── GlobalUsings.cs                 # Global using statements
│   │
│   └── Docman.Core.Application/            # Application layer
│       ├── ApplicationDependencyInjection.cs
│       ├── Common/
│       │   ├── Behaviors/
│       │   │   └── ValidationBehavior.cs   # MediatR pipeline behaviors
│       │   ├── DTOs/
│       │   │   └── UserDto.cs              # Data transfer objects
│       │   ├── Errors/
│       │   │   ├── UserErrors.cs           # Domain-specific errors
│       │   │   ├── ProductErrors.cs
│       │   │   └── ValidationErrors.cs
│       │   ├── Mappings/
│       │   │   └── UserMappingConfig.cs    # Mapster configurations
│       │   └── Results/
│       │       ├── Error.cs                # Error handling types
│       │       └── Result.cs               # Result pattern implementation
│       ├── Contracts/
│       │   ├── IGenericRepository.cs       # Repository interfaces
│       │   ├── IUnitOfWork.cs             # Unit of work pattern
│       │   └── Specifications/             # Specification pattern
│       ├── Features/                       # Feature-based organization
│       │   └── Users/
│       │       ├── Commands/               # CQRS command handlers
│       │       │   ├── CreateUser/
│       │       │   │   ├── CreateUserCommand.cs
│       │       │   │   ├── CreateUserCommandHandler.cs
│       │       │   │   ├── CreateUserCommandValidator.cs
│       │       │   │   └── CreateUserResponse.cs
│       │       │   ├── UpdateUser/
│       │       │   └── DeleteUser/
│       │       └── Queries/                # CQRS query handlers
│       │           ├── GetAllUsers/
│       │           ├── GetUserById/
│       │           └── GetUsersByRole/
│       └── Services/                       # Application services
│
├── Infrastructure/                          # Infrastructure layer folder
│   ├── Docman.Infrastructure.Persistence/  # Data access layer
│   │   ├── Configurations/
│   │   │   └── UserConfiguration.cs       # Entity Framework configurations
│   │   ├── Context/
│   │   │   └── DocmanDbContext.cs         # Database context
│   │   ├── Migrations/                     # EF Core migrations
│   │   ├── Repositories/
│   │   │   ├── GenericRepository.cs       # Generic repository implementation
│   │   │   └── UnitOfWork.cs              # Unit of work implementation
│   │   └── DependencyInjection.cs
│   │
│   └── Docman.Infrastructure.Integration/ # External services layer
│       ├── Cache/                         # Caching implementations
│       ├── Email/                         # Email service implementations
│       ├── Storage/                       # File storage implementations
│       └── DependencyInjection.cs
│
└── Presentation/                           # Presentation layer folder
    └── Docman.Presentation.API/           # Web API layer
        ├── Controllers/
        │   ├── Base/
        │   │   └── BaseController.cs      # Base controller with common functionality
        │   └── UsersController.cs         # Feature-specific controllers
        ├── Common/
        │   └── ResultExtensions.cs        # Extension methods for result handling
        ├── Exceptions/
        │   └── GlobalExceptionHandler.cs  # Global exception handling
        ├── DependencyInjection.cs
        └── Program.cs                      # Application entry point
```

---

## Layer Responsibilities

### 1. Domain Layer (`Docman.Core.Domain`)

**Purpose**: Contains the core business logic, entities, and domain rules.

**Responsibilities**:

- Define business entities with their properties and behavior
- Implement domain-specific business rules and validations
- Provide base classes and interfaces for common functionality
- Define domain events and specifications
- Contains no dependencies on external frameworks

**Key Components**:

- **Entities**: Business objects with identity (`User.cs`)
- **Value Objects**: Objects without identity that represent concepts
- **Domain Services**: Services that contain domain logic not belonging to entities
- **Base Classes**: Common functionality for all entities (`BaseEntity<TKey>`)

**Example Structure**:

```csharp
// Domain Entity
public class User : BaseEntity<Guid>
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTimeOffset? LastLoginAt { get; set; }
}

// Base Entity with Audit Properties
public abstract class BaseEntity<TKey> : IAuditableEntity
{
    public TKey Id { get; set; } = default!;
    public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
    public DateTimeOffset? UpdatedAt { get; set; }
    public bool IsDeleted { get; set; } = false;
    public DateTimeOffset? DeletedAt { get; set; }
}
```

### 2. Application Layer (`Docman.Core.Application`)

**Purpose**: Orchestrates business workflows and handles application logic.

**Responsibilities**:

- Implement use cases and business workflows
- Handle CQRS commands and queries using MediatR
- Validate input data using FluentValidation
- Map between domain entities and DTOs
- Define repository and service contracts
- Implement cross-cutting concerns (behaviors)

**Key Components**:

- **Features**: Organized by business features using CQRS pattern
- **Commands**: Write operations that modify state
- **Queries**: Read operations that return data
- **Handlers**: Process commands and queries
- **Validators**: Validate input using FluentValidation
- **DTOs**: Data transfer objects for external communication
- **Contracts**: Interfaces for repositories and services

**Example Structure**:

```csharp
// Command
public sealed record CreateUserCommand(
    string FirstName,
    string LastName,
    string Email,
    bool IsActive = true
) : IRequest<Result<CreateUserResponse>>;

// Command Handler
public sealed class CreateUserCommandHandler(
    IGenericRepository<User, Guid> userRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreateUserCommand, Result<CreateUserResponse>>
{
    public async Task<Result<CreateUserResponse>> Handle(
        CreateUserCommand request,
        CancellationToken cancellationToken)
    {
        var user = request.Adapt<User>();
        await userRepository.AddAsync(user, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        var response = user.Adapt<CreateUserResponse>();
        return Result.Success(response);
    }
}
```

### 3. Infrastructure Layer

#### 3.1 Persistence (`Docman.Infrastructure.Persistence`)

**Purpose**: Handles data access and persistence concerns.

**Responsibilities**:

- Implement repository patterns defined in Application layer
- Configure Entity Framework mappings and relationships
- Handle database migrations and schema changes
- Implement Unit of Work pattern for transaction management
- Provide database context and connection management

**Key Components**:

- **Repositories**: Concrete implementations of repository interfaces
- **Configurations**: Entity Framework entity configurations
- **Context**: Database context for Entity Framework
- **Migrations**: Database schema versioning

#### 3.2 Integration (`Docman.Infrastructure.Integration`)

**Purpose**: Handles external service integrations and cross-cutting concerns.

**Responsibilities**:

- Implement caching mechanisms (Redis, In-Memory)
- Handle email service integrations
- Manage file storage operations (local, cloud)
- Integrate with external APIs and services
- Implement logging and monitoring

**Key Components**:

- **Cache**: Caching service implementations
- **Email**: Email service providers
- **Storage**: File storage abstractions and implementations

### 4. Presentation Layer (`Docman.Presentation.API`)

**Purpose**: Handles HTTP requests and responses, API documentation, and user interface concerns.

**Responsibilities**:

- Define API endpoints and routing
- Handle HTTP request/response mapping
- Implement authentication and authorization
- Provide API documentation (Swagger/OpenAPI)
- Handle global exception management
- Validate and transform external requests to application commands/queries

**Key Components**:

- **Controllers**: API endpoints organized by feature
- **Middleware**: Cross-cutting HTTP concerns
- **Extensions**: Helper methods for common operations
- **Configuration**: API-specific settings and services

**Example Structure**:

```csharp
[HttpPost]
public async Task<IActionResult> CreateUser(
    [FromBody] CreateUserCommand command,
    CancellationToken cancellationToken = default)
{
    var result = await mediator.Send(command, cancellationToken);

    if (result.IsFailure)
        return result.ToActionResult();

    return result.ToActionResult(StatusCodes.Status201Created);
}
```

---

## Naming Conventions

### Classes

#### Entities

- **Pattern**: `{EntityName}` (Singular, PascalCase)
- **Examples**: `User`, `Product`, `Category`
- **Location**: `Docman.Core.Domain.Entities`

#### Commands

- **Pattern**: `{Action}{EntityName}Command`
- **Examples**: `CreateUserCommand`, `UpdateProductCommand`, `DeleteCategoryCommand`
- **Location**: `Docman.Core.Application.Features.{EntityName}.Commands.{Action}{EntityName}`

#### Queries

- **Pattern**: `{Action}{EntityName}Query`
- **Examples**: `GetUserByIdQuery`, `GetAllProductsQuery`, `GetUsersByRoleQuery`
- **Location**: `Docman.Core.Application.Features.{EntityName}.Queries.{Action}{EntityName}`

#### Handlers

- **Pattern**: `{CommandOrQueryName}Handler`
- **Examples**: `CreateUserCommandHandler`, `GetUserByIdQueryHandler`
- **Location**: Same folder as corresponding command/query

#### Validators

- **Pattern**: `{CommandOrQueryName}Validator`
- **Examples**: `CreateUserCommandValidator`, `GetUserByIdQueryValidator`
- **Location**: Same folder as corresponding command/query

#### Responses

- **Pattern**: `{Action}{EntityName}Response`
- **Examples**: `CreateUserResponse`, `GetUserByIdResponse`
- **Location**: Same folder as corresponding command/query

#### Controllers

- **Pattern**: `{EntityName}Controller` (Plural entity name)
- **Examples**: `UsersController`, `ProductsController`
- **Location**: `Docman.Presentation.API.Controllers`

#### Repositories

- **Pattern**: `{EntityName}Repository` or `GenericRepository<T, TKey>`
- **Examples**: `UserRepository`, `GenericRepository<User, Guid>`
- **Location**: `Docman.Infrastructure.Persistence.Repositories`

#### Services

- **Pattern**: `{Purpose}Service` with interface `I{Purpose}Service`
- **Examples**: `EmailService/IEmailService`, `CacheService/ICacheService`
- **Location**: Implementation in Infrastructure, Interface in Application

### Variables and Properties

#### Private Fields

- **Pattern**: `_{fieldName}` (camelCase with underscore prefix)
- **Examples**: `_context`, `_userRepository`, `_logger`

#### Method Parameters

- **Pattern**: `{parameterName}` (camelCase)
- **Examples**: `userId`, `cancellationToken`, `includeDeleted`

#### Properties

- **Pattern**: `{PropertyName}` (PascalCase)
- **Examples**: `FirstName`, `CreatedAt`, `IsActive`

#### Local Variables

- **Pattern**: `{variableName}` (camelCase)
- **Examples**: `user`, `result`, `query`

### Methods and Functions

#### Repository Methods

- **Query Methods**: `Get{Description}Async`, `Any{Description}Async`
  - Examples: `GetByIdAsync`, `GetAllAsync`, `AnyAsync`
- **Command Methods**: `{Action}Async` or `{Action}` (for sync operations)
  - Examples: `AddAsync`, `Update`, `Delete`

#### Service Methods

- **Pattern**: `{Action}{Object}Async`
- **Examples**: `SendEmailAsync`, `CacheValueAsync`, `ValidateUserAsync`

#### Handler Methods

- **Pattern**: `Handle` (required by MediatR interface)
- **Signature**: `Task<TResponse> Handle(TRequest request, CancellationToken cancellationToken)`

#### Controller Actions

- **Pattern**: `{HttpVerb}{EntityAction}`
- **Examples**: `GetUserById`, `CreateUser`, `UpdateUser`, `DeleteUser`

### Folders and Files

#### Project Structure

- **Pattern**: `{CompanyName}.{LayerName}.{SubLayer}`
- **Examples**: `Docman.Core.Domain`, `Docman.Infrastructure.Persistence`

#### Feature Folders

- **Pattern**: `{EntityName}` (Plural for controllers, singular for features)
- **Examples**: `Users/`, `Products/`, `Categories/`

#### Command/Query Folders

- **Pattern**: `{Action}{EntityName}/`
- **Examples**: `CreateUser/`, `GetUserById/`, `UpdateProduct/`

#### File Names

- **Classes**: `{ClassName}.cs`
- **Interfaces**: `I{InterfaceName}.cs`
- **Extensions**: `{Purpose}Extensions.cs`
- **Examples**: `User.cs`, `IUserRepository.cs`, `ResultExtensions.cs`

---

## Namespace & File Scope

### Namespace Structure Rules

#### 1. Namespace Hierarchy Mapping

Namespaces **MUST** directly mirror the folder structure:

```
Folder: Docman.Core.Application/Features/Users/<USER>/CreateUser/
Namespace: Docman.Core.Application.Features.Users.Commands.CreateUser

Folder: Docman.Infrastructure.Persistence/Repositories/
Namespace: Docman.Infrastructure.Persistence.Repositories

Folder: Docman.Presentation.API/Controllers/
Namespace: Docman.Presentation.API.Controllers
```

#### 2. Root Namespace Alignment

- **Project Name** = **Root Namespace**
- Project: `Docman.Core.Domain.csproj` → Root Namespace: `Docman.Core.Domain`
- Project: `Docman.Presentation.API.csproj` → Root Namespace: `Docman.Presentation.API`

#### 3. Namespace Examples by Layer

##### Domain Layer

```csharp
// File: Docman.Core.Domain/Entities/User.cs
namespace Docman.Core.Domain.Entities;

// File: Docman.Core.Domain/Common/BaseEntity.cs
namespace Docman.Core.Domain.Common;
```

##### Application Layer

```csharp
// File: Docman.Core.Application/Features/Users/<USER>/CreateUser/CreateUserCommand.cs
namespace Docman.Core.Application.Features.Users.Commands.CreateUser;

// File: Docman.Core.Application/Contracts/IGenericRepository.cs
namespace Docman.Core.Application.Contracts;

// File: Docman.Core.Application/Common/Results/Result.cs
namespace Docman.Core.Application.Common.Results;
```

##### Infrastructure Layer

```csharp
// File: Docman.Infrastructure.Persistence/Repositories/GenericRepository.cs
namespace Docman.Infrastructure.Persistence.Repositories;

// File: Docman.Infrastructure.Persistence/Context/DocmanDbContext.cs
namespace Docman.Infrastructure.Persistence.Context;

// File: Docman.Infrastructure.Integration/Cache/CacheService.cs
namespace Docman.Infrastructure.Integration.Cache;
```

##### Presentation Layer

```csharp
// File: Docman.Presentation.API/Controllers/UsersController.cs
namespace Docman.Presentation.API.Controllers;

// File: Docman.Presentation.API/Common/ResultExtensions.cs
namespace Docman.Presentation.API.Common;
```

### File Scope Guidelines

#### 1. Single Responsibility

- **One public class per file** (except for tightly coupled helper classes)
- **File name MUST match the primary public class name**
- Related small classes (like DTOs) can be in the same file if they're only used together

#### 2. GlobalUsings.cs Files

Each project contains a `GlobalUsings.cs` file for commonly used namespaces:

```csharp
// Example: Docman.Core.Application/GlobalUsings.cs
global using MediatR;
global using FluentValidation;
global using Docman.Core.Application.Common.Results;
global using Docman.Core.Domain.Common;
```

#### 3. File Organization Within Features

```
Features/Users/<USER>/CreateUser/
├── CreateUserCommand.cs          # Command definition
├── CreateUserCommandHandler.cs   # Command handler
├── CreateUserCommandValidator.cs # Validation rules
└── CreateUserResponse.cs         # Response DTO
```

#### 4. Using Statements Policy

- **Minimize explicit using statements** by leveraging `GlobalUsings.cs`
- **Order using statements**: System namespaces first, then third-party, then project namespaces
- **Use file-scoped namespaces** for cleaner code (C# 10+ feature)

```csharp
// Good: File-scoped namespace
using Docman.Core.Domain.Entities;
using Mapster;

namespace Docman.Core.Application.Features.Users.Commands.CreateUser;

public sealed class CreateUserCommandHandler(
    IGenericRepository<User, Guid> userRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreateUserCommand, Result<CreateUserResponse>>
{
    // Implementation
}
```

#### 5. Namespace Import Rules

- **Domain layer**: Should have minimal external dependencies
- **Application layer**: Can reference Domain, but not Infrastructure or Presentation
- **Infrastructure layer**: Can reference Domain and Application
- **Presentation layer**: Can reference all other layers

#### 6. Cross-Layer Communication

```csharp
// ✅ Correct: Presentation → Application
using Docman.Core.Application.Features.Users.Commands.CreateUser;

// ✅ Correct: Application → Domain
using Docman.Core.Domain.Entities;

// ❌ Incorrect: Domain → Application
// Domain should never reference Application layer

// ❌ Incorrect: Application → Infrastructure
// Application should only reference Infrastructure through interfaces
```

This structure ensures clean separation of concerns, maintainable code organization, and adherence to Clean Architecture principles.
