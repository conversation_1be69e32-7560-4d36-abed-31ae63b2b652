﻿namespace Docman.Core.Application.Common.Errors;

public static class UserErrors
{
    public static readonly Error InvalidCredentials = new("User.InvalidCredentials", "Invalid email or password.");
    
    public static readonly Error EmailAlreadyExists = new("User.EmailExists", "Email is already registered.");

    public static readonly Error LockedOut = new("User.Locked Out", "User Locked Out");

    public static readonly Error InvalidRefreshToken = new("User.InvalidRefreshToken", "Invalid Refresh Token");
    public static readonly Error UserNotFound = new("User.NotFound", "The user has not been found");
}