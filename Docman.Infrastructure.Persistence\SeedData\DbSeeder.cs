﻿using Docman.Infrastructure.Persistence.Context;
using Microsoft.Extensions.Logging;

namespace Docman.Infrastructure.Persistence.SeedData;

public static class DbSeeder
{
    public static async Task SeedAsync(DocmanDbContext context, ILogger? logger = null)
    {
        try
        {
            logger?.LogInformation("Starting database seeding...");
            
            // Seed in proper order to respect foreign key constraints
            await CountrySeeder.SeedAsync(context, logger);
            
            // add other seeders here as needed
            // await UserSeeder.SeedAsync(context, logger);
            // await RoleSeeder.SeedAsync(context, logger);
            // await DocumentTypeSeeder.SeedAsync(context, logger);
            
            logger?.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Error occurred during database seeding");
            throw;
        }
    }

    // Overload for backward compatibility
    public static async Task SeedAsync(DocmanDbContext context)
    {
        await SeedAsync(context, null);
    }
}