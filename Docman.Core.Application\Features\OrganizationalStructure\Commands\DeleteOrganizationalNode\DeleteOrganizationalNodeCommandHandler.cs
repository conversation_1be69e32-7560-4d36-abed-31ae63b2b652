using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.DeleteOrganizationalNode;

/// <summary>
/// Hand<PERSON> for deleting organizational nodes with hierarchy validation
/// </summary>
public sealed class DeleteOrganizationalNodeCommandHandler(
    IOrganizationalNodeRepository organizationalNodeRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<DeleteOrganizationalNodeCommand, Result>
{
    public async Task<Result> Handle(
        DeleteOrganizationalNodeCommand request,
        CancellationToken cancellationToken)
    {
        // Get the existing node
        var existingNode = await organizationalNodeRepository.GetByCodeAsync(
            request.Code,
            cancellationToken: cancellationToken);

        if (existingNode is null)
            return Result.Failure(OrganizationalNodeErrors.NodeNotFound);

        // Check if node has children
        var hasChildren = await organizationalNodeRepository.HasChildrenAsync(
            request.Code,
            cancellationToken);

        if (hasChildren && !request.CascadeDelete)
            return Result.Failure(OrganizationalNodeErrors.CannotDeleteWithChildren);

        // Perform the deletion
        var deletedCount = await organizationalNodeRepository.HardDeleteAsync(
            request.Code,
            cascadeDelete: request.CascadeDelete,
            cancellationToken: cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success($"Node with code '{request.Code}' deleted successfully. {deletedCount} nodes deleted.");
    }
}