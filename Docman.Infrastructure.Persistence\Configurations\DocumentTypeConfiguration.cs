namespace Docman.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for DocumentType entity
/// </summary>
public class DocumentTypeConfiguration : IEntityTypeConfiguration<DocumentType>
{
    public void Configure(EntityTypeBuilder<DocumentType> builder)
    {
        // Table name
        builder.ToTable("DocumentTypes");

        // Primary key - Id is the primary key
        builder.HasKey(dt => dt.Id);

        // Properties configuration
        builder.Property(dt => dt.Id)
            .IsRequired()
            .ValueGeneratedOnAdd()
            .HasComment("Unique identifier of the document type (Primary Key)");

        builder.Property(dt => dt.Name)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("Name of the document type (must be unique)");

        builder.Property(dt => dt.Status)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("Current status of the document type (0=Unapproved, 1=Approved, 2=Frozen)");

        // Indexes
        builder.HasIndex(dt => dt.Name)
            .IsUnique()
            .HasDatabaseName("IX_DocumentTypes_Name");

        // Default values
        builder.Property(dt => dt.Status)
            .HasDefaultValue(DocumentTypeStatus.Unapproved);
    }
}
