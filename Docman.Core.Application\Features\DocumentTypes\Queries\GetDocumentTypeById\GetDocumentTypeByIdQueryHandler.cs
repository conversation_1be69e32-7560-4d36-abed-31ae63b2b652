using Docman.Core.Application.Common.DTOs.DocumentTypes;
using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.DocumentTypes.Queries.GetDocumentTypeById;

/// <summary>
/// Handler for GetDocumentTypeByIdQuery
/// </summary>
public sealed class GetDocumentTypeByIdQueryHandler(
    IGenericRepository<DocumentType> documentTypeRepository
) : IRequestHandler<GetDocumentTypeByIdQuery, Result<DocumentTypeDetailsDto>>
{
    public async Task<Result<DocumentTypeDetailsDto>> Handle(GetDocumentTypeByIdQuery request, CancellationToken cancellationToken)
    {
        var documentType = await documentTypeRepository.GetAsync(
            filter: dt => dt.Id == request.Id,
            cancellationToken: cancellationToken);

        if (documentType == null)
        {
            return Result.Failure<DocumentTypeDetailsDto>(DocumentTypeErrors.DocumentTypeNotFound);
        }

        var documentTypeDto = documentType.Adapt<DocumentTypeDetailsDto>();

        return Result.Success(documentTypeDto);
    }
}
