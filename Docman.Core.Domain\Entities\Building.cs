namespace Docman.Core.Domain.Entities;

public class Building : AuditableEntity<int>
{

    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Detailed address description of the building (required)
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the building
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Optional email contact for the building
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Optional mobile number contact for the building
    /// </summary>
    public string? MobileNumber { get; set; }

    /// <summary>
    /// Current status of the building (default: Unapproved)
    /// </summary>
    public BuildingStatus Status { get; set; } = BuildingStatus.Unapproved;

    /// <summary>
    /// Foreign key to DistrictOrVillage table
    /// </summary>
    public string DistrictOrVillageCode { get; set; } = string.Empty;
    
    public DistrictOrVillage DistrictOrVillage { get; set; } = null!;
}
