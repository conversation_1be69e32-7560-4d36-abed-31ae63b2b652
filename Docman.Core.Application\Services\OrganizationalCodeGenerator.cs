namespace Docman.Core.Application.Services;

/// <summary>
/// Application service responsible for generating organizational codes
/// </summary>
public static class OrganizationalCodeGenerator
{
    /// <summary>
    /// Gets the next available code at the specified level with the same parent
    /// </summary>
    /// <param name="level">The organizational level</param>
    /// <param name="parentCode">The parent code (null for L1)</param>
    /// <param name="existingCodes">List of existing codes to avoid duplicates</param>
    /// <returns>Result containing the next available code or an error</returns>
    public static Result<string> GetNextCodeAtLevel(OrganizationalLevel level, string? parentCode, IEnumerable<string> existingCodes)
    {
        var existingSet = new HashSet<string>(existingCodes, StringComparer.OrdinalIgnoreCase);
        
        return level switch
        {
            OrganizationalLevel.L1 => GetNextL1Code(existingSet),
            OrganizationalLevel.L2 => GetNextL2Code(parentCode!, existingSet),
            OrganizationalLevel.L3 => GetNextL3Code(parentCode!, existingSet),
            OrganizationalLevel.L4 => GetNextL4Code(parentCode!, existingSet),
            _ => Result.Failure<string>(OrganizationalNodeErrors.InvalidLevel)
        };
    }

    private static Result<string> GetNextL1Code(HashSet<string> existingCodes)
    {
        // L1 pattern: 1X000
        for (var i = 1; i < 36; i++)
        {
            var secondChar = i < 10 ? i.ToString() : ((char)('A' + i - 10)).ToString();
            var code = "1" + secondChar + "000";
            
            if (!existingCodes.Contains(code))
                return Result.Success(code);
        }
        return Result.Failure<string>(OrganizationalNodeErrors.NoAvailableCodes);
    }

    private static Result<string> GetNextL2Code(string parentCode, HashSet<string> existingCodes)
    {
        // L2 pattern: XXY00 (first 2 chars from parent + new 3rd char + 00)
        var prefix = parentCode[..2];
        
        for (int i = 0; i < 36; i++)
        {
            var thirdChar = i < 10 ? i.ToString() : ((char)('A' + i - 10)).ToString();
            var code = prefix + thirdChar + "00";
            
            if (!existingCodes.Contains(code))
                return Result.Success(code);
        }
        return Result.Failure<string>(OrganizationalNodeErrors.NoAvailableCodes);
    }

    private static Result<string> GetNextL3Code(string parentCode, HashSet<string> existingCodes)
    {
        // L3 pattern: XXXY0 (first 3 chars from parent + new 4th char + 0)
        var prefix = parentCode[..3];
        
        for (var i = 0; i < 36; i++)
        {
            var fourthChar = i < 10 ? i.ToString() : ((char)('A' + i - 10)).ToString();
            var code = prefix + fourthChar + "0";
            
            if (!existingCodes.Contains(code))
                return Result.Success(code);
        }
        return Result.Failure<string>(OrganizationalNodeErrors.NoAvailableCodes);
    }

    private static Result<string> GetNextL4Code(string parentCode, HashSet<string> existingCodes)
    {
        // L4 pattern: XXXXZ (first 4 chars from parent + new 5th char)
        var prefix = parentCode[..4];
        
        for (var i = 0; i < 36; i++)
        {
            var fifthChar = i < 10 ? i.ToString() : ((char)('A' + i - 10)).ToString();
            var code = prefix + fifthChar;
            
            if (!existingCodes.Contains(code))
                return Result.Success(code);
        }
        return Result.Failure<string>(OrganizationalNodeErrors.NoAvailableCodes);
    }
}
