using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.CreateOrganizationalNode;

/// <summary>
/// Hand<PERSON> for creating organizational nodes with automatic code generation and hierarchy validation
/// </summary>
public sealed class CreateOrganizationalNodeCommandHandler(
    IOrganizationalNodeRepository organizationalNodeRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreateOrganizationalNodeCommand, Result<OrganizationalNodeDto>>
{
    public async Task<Result<OrganizationalNodeDto>> Handle(
        CreateOrganizationalNodeCommand request,
        CancellationToken cancellationToken)
    {
        // Sanitize input data
        var (name, description, parentCode) = SanitizeRequest(request);

        // Validate parent node existence and determine target level
        var parentValidationResult = await ValidateParentAndDetermineLevel(parentCode, cancellationToken);

        if (parentValidationResult.IsFailure)
            return Result.Failure<OrganizationalNodeDto>(parentValidationResult!.Error);

        var targetLevel = parentValidationResult.Value;

        // Generate the organizational code
        var codeGenerationResult = await GenerateOrganizationalCode(targetLevel, parentCode, cancellationToken);
        if (codeGenerationResult.IsFailure)
            return Result.Failure<OrganizationalNodeDto>(codeGenerationResult.Error);

        var generatedCode = codeGenerationResult.Value;

        // Validate business rules before creating the entity
        var businessValidationResult = await ValidateBusinessRules(name, parentCode, cancellationToken);
        if (businessValidationResult.IsFailure)
            return Result.Failure<OrganizationalNodeDto>(businessValidationResult.Error);

        // Create and persist the organizational node
        var organizationalNode = CreateOrganizationalNodeEntity(generatedCode, name, description);

        await organizationalNodeRepository.AddAsync(organizationalNode, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        // Map to DTO and return
        var result = organizationalNode.Adapt<OrganizationalNodeDto>();
        return Result.Success(result);
    }

    /// <summary>
    /// Sanitizes the input request data by trimming strings and normalizing values
    /// </summary>
    private static (string Name, string? Description, string? ParentCode) SanitizeRequest(
        CreateOrganizationalNodeCommand request)
    {
        return (
            Name: request.Name?.Trim() ?? string.Empty,
            Description: string.IsNullOrWhiteSpace(request.Description) ? null : request.Description.Trim(),
            ParentCode: string.IsNullOrWhiteSpace(request.ParentCode)
                ? null
                : request.ParentCode.Trim().ToUpperInvariant()
        );
    }

    /// <summary>
    /// Validates parent node existence and determines the target level for the new node
    /// </summary>
    private async Task<Result<OrganizationalLevel>> ValidateParentAndDetermineLevel(
        string? parentCode,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(parentCode))
            return Result.Success(OrganizationalLevel.L1);

        // Validate parent exists
        var parentNode =
            await organizationalNodeRepository.GetByCodeAsync(parentCode, cancellationToken: cancellationToken);
        if (parentNode is null)
            return Result.Failure<OrganizationalLevel>(OrganizationalNodeErrors.ParentNotFound);

        // Determine target level based on parent level
        var parentLevel = parentNode.Level;

        var targetLevel = parentLevel switch
        {
            OrganizationalLevel.L1 => OrganizationalLevel.L2,
            OrganizationalLevel.L2 => OrganizationalLevel.L3,
            OrganizationalLevel.L3 => OrganizationalLevel.L4,
            OrganizationalLevel.L4 => null,
            _ => default(OrganizationalLevel?)
        };

        if (targetLevel is null)
        {
            return parentLevel == OrganizationalLevel.L4
                ? Result.Failure<OrganizationalLevel>(OrganizationalNodeErrors.MaxLevelExceeded)
                : Result.Failure<OrganizationalLevel>(OrganizationalNodeErrors.InvalidLevel);
        }

        return Result.Success(targetLevel.Value);
    }

    /// <summary>
    /// Generates the organizational code for the new node
    /// </summary>
    private async Task<Result<string>> GenerateOrganizationalCode(
        OrganizationalLevel targetLevel,
        string? parentCode,
        CancellationToken cancellationToken)
    {
        var existingCodes = await organizationalNodeRepository.GetAllExistingCodesAsync(cancellationToken);
        return OrganizationalCodeGenerator.GetNextCodeAtLevel(targetLevel, parentCode, existingCodes);
    }

    /// <summary>
    /// Validates business rules such as duplicate names at the same level
    /// </summary>
    private async Task<Result> ValidateBusinessRules(
        string name,
        string? parentCode,
        CancellationToken cancellationToken)
    {
        var hasDuplicateName = await HasDuplicateNameAtLevel(name, parentCode, cancellationToken);
        return hasDuplicateName
            ? Result.Failure(OrganizationalNodeErrors.DuplicateNameInLevel)
            : Result.Success();
    }

    /// <summary>
    /// Checks if a name already exists at the same organizational level
    /// </summary>
    private async Task<bool> HasDuplicateNameAtLevel(
        string name,
        string? parentCode,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(parentCode))
        {
            // For L1 nodes, check other L1 nodes
            var l1Nodes = await organizationalNodeRepository.GetNodesByLevelAsync(
                OrganizationalLevel.L1,
                cancellationToken);

            return l1Nodes.Any(n => string.Equals(n.Name, name, StringComparison.OrdinalIgnoreCase));
        }

        // For other levels, check siblings
        var siblings = await organizationalNodeRepository.GetChildrenAsync(parentCode, cancellationToken);
        return siblings.Any(n => string.Equals(n.Name, name, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Creates the organizational node entity with all required properties
    /// </summary>
    private static OrganizationalNode CreateOrganizationalNodeEntity(
        string generatedCode,
        string name,
        string? description)
    {
        return new OrganizationalNode
        {
            Code = generatedCode.ToUpperInvariant(),
            Level = OrganizationalCodeHelper.DetermineLevel(generatedCode),
            ParentCode = OrganizationalCodeHelper.DeriveParentCode(generatedCode),
            Name = name,
            Description = description
        };
    }
}