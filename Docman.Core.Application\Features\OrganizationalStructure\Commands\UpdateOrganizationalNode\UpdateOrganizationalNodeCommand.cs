namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.UpdateOrganizationalNode;

/// <summary>
/// Command to update an existing organizational node
/// Note: Code cannot be changed to maintain hierarchy integrity
/// </summary>
public sealed record UpdateOrganizationalNodeCommand(
    string Code,
    string Name,
    string? Description
) : IRequest<Result<OrganizationalNodeDto>>;
