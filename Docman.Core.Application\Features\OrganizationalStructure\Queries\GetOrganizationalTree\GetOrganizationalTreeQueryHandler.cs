using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Queries.GetOrganizationalTree;

/// <summary>
/// <PERSON><PERSON> for retrieving the complete organizational tree structure
/// </summary>
public sealed class GetOrganizationalTreeQueryHandler(
    IOrganizationalNodeRepository organizationalNodeRepository
) : IRequestHandler<GetOrganizationalTreeQuery, Result<OrganizationalTreeDto>>
{
    public async Task<Result<OrganizationalTreeDto>> Handle(
        GetOrganizationalTreeQuery request,
        CancellationToken cancellationToken)
    {
        // Get all nodes
        var allNodes = await organizationalNodeRepository.GetCompleteTreeAsync(
            cancellationToken);

        // Get level counts
        var levelCounts = await organizationalNodeRepository.GetLevelCountsAsync(
            cancellationToken);

        // Remove duplicates if any exist and build distinct node map
        var distinctNodes = allNodes
            .GroupBy(n => n.Code)
            .Select(g => g.First())
            .ToList();

        // Build the tree structure
        var nodeMap = distinctNodes.ToDictionary(n => n.Code, n => n.Adapt<OrganizationalTreeNodeDto>());
        var rootNodes = new List<OrganizationalTreeNodeDto>();

        // First pass: identify root nodes
        foreach (var node in distinctNodes.Where(n => n.Level == OrganizationalLevel.L1))
        {
            if (nodeMap.TryGetValue(node.Code, out var rootNodeDto))
            {
                rootNodes.Add(rootNodeDto);
            }
        }

        // Second pass: build parent-child relationships for non-root nodes
        foreach (var node in distinctNodes.Where(n => n.Level != OrganizationalLevel.L1))
        {
            if (node.ParentCode != null && 
                nodeMap.TryGetValue(node.Code, out var childNodeDto) &&
                nodeMap.TryGetValue(node.ParentCode, out var parentNodeDto))
            {
                // Check if child is not already in parent's children to prevent duplicates
                if (!parentNodeDto.Children.Any(c => c.Code == childNodeDto.Code))
                {
                    parentNodeDto.Children.Add(childNodeDto);
                }
            }
        }

        // Sort children at each level
        SortChildrenRecursively(rootNodes);

        var result = new OrganizationalTreeDto
        {
            RootNodes = rootNodes,
            LevelCounts = levelCounts,
            TotalNodes = distinctNodes.Count
        };

        return Result.Success(result);
    }

    /// <summary>
    /// Recursively sorts children by code for natural hierarchy order
    /// </summary>
    private static void SortChildrenRecursively(List<OrganizationalTreeNodeDto> nodes)
    {
        foreach (var node in nodes)
        {
            node.Children = node.Children.OrderBy(c => c.Code).ToList();
            SortChildrenRecursively(node.Children);
        }
    }
}