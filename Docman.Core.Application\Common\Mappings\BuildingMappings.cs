using Docman.Core.Application.Common.DTOs.Buildings;

namespace Docman.Core.Application.Common.Mappings;

public class BuildingMappings : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<Building, BuildingListDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Status, src => src.Status)
            .Map(dest => dest.VillageNameAr, src => src.DistrictOrVillage.NameAr)
            .Map(dest => dest.CityOrDistrictNameAr, src => src.DistrictOrVillage.CityOrDistrict.NameAr)
            .Map(dest => dest.CityOrDistrictNameEn, src => src.DistrictOrVillage.CityOrDistrict.NameEn)
            .Map(dest => dest.GovernorateNameAr, src => src.DistrictOrVillage.CityOrDistrict.Governorate.NameAr)
            .Map(dest => dest.GovernorateNameEn, src => src.DistrictOrVillage.CityOrDistrict.Governorate.NameEn)
            .Map(dest => dest.CountryNameAr, src => src.DistrictOrVillage.CityOrDistrict.Governorate.Country.NameAr)
            .Map(dest => dest.CountryNameEn, src => src.DistrictOrVillage.CityOrDistrict.Governorate.Country.NameEn);

        config.NewConfig<Building, BuildingDetailsDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Address, src => src.Address)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.Email, src => src.Email)
            .Map(dest => dest.MobileNumber, src => src.MobileNumber)
            .Map(dest => dest.Status, src => src.Status)
            .Map(dest => dest.DistrictOrVillageCode, src => src.DistrictOrVillageCode)
            .Map(dest => dest.VillageNameAr, src => src.DistrictOrVillage.NameAr)
            .Map(dest => dest.CityOrDistrictNameAr, src => src.DistrictOrVillage.CityOrDistrict.NameAr)
            .Map(dest => dest.CityOrDistrictNameEn, src => src.DistrictOrVillage.CityOrDistrict.NameEn)
            .Map(dest => dest.GovernorateNameAr, src => src.DistrictOrVillage.CityOrDistrict.Governorate.NameAr)
            .Map(dest => dest.GovernorateNameEn, src => src.DistrictOrVillage.CityOrDistrict.Governorate.NameEn)
            .Map(dest => dest.CountryNameAr, src => src.DistrictOrVillage.CityOrDistrict.Governorate.Country.NameAr)
            .Map(dest => dest.CountryNameEn, src => src.DistrictOrVillage.CityOrDistrict.Governorate.Country.NameEn)
            // Audit properties
            .Map(dest => dest.CreatedByID, src => src.CreatedByID)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.UpdatedByID, src => src.UpdatedByID)
            .Map(dest => dest.UpdatedAt, src => src.UpdatedAt);
    }
}