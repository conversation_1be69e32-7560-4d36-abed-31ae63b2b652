namespace Docman.Core.Application.Features.DocumentTypes.Commands.UpdateDocumentType;

/// <summary>
/// Validator for UpdateDocumentTypeCommand
/// </summary>
public sealed class UpdateDocumentTypeCommandValidator : AbstractValidator<UpdateDocumentTypeCommand>
{
    public UpdateDocumentTypeCommandValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Document type ID must be a positive number.");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Document type name is required.")
            .MaximumLength(255)
            .WithMessage("Document type name must not exceed 255 characters.")
            .Matches(@"^[\u0600-\u06FFa-zA-Z0-9\s\-_\.]+$")
            .WithMessage("Document type name can only contain Arabic letters, English letters, numbers, spaces, hyphens, underscores, and dots.");

        RuleFor(x => x.Status)
            .IsInEnum()
            .WithMessage("Status must be a valid DocumentTypeStatus value.");
    }
}

