namespace Docman.Core.Application.Features.DocumentTypes.Commands.ChangeDocumentTypeStatus;

/// <summary>
/// Handler for ChangeDocumentTypeStatusCommand
/// </summary>
public sealed class ChangeDocumentTypeStatusCommandHandler(
    IGenericRepository<DocumentType> documentTypeRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<ChangeDocumentTypeStatusCommand, Result>
{
    public async Task<Result> Handle(ChangeDocumentTypeStatusCommand request, CancellationToken cancellationToken)
    {
        // Find the existing document type by ID
        var documentType = await documentTypeRepository.GetAsync(
            filter: dt => dt.Id == request.Id,
            cancellationToken: cancellationToken);

        if (documentType == null)
        {
            return Result.Failure(DocumentTypeErrors.DocumentTypeNotFound);
        }

        // Update status
        documentType.Status = request.Status;

        // Update in repository
        await documentTypeRepository.UpdateAsync(documentType, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

