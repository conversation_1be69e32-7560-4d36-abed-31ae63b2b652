{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=db25270.public.databaseasp.net; Database=db25270; User Id=db25270; Password=************; Encrypt=False; MultipleActiveResultSets=True;"}, "Email": {"Host": "smtp.gmail.com", "Port": 587, "EnableSsl": true, "From": "<EMAIL>", "Username": "", "Password": ""}, "Storage": {"Provider": "Local", "RootPath": "uploads"}, "ExternalApis": {"Profiles": {"BaseUrl": "https://api.example.com/"}}, "Jwt": {"Key": "1K6MArlAgWHH2RZH4coe21kT9sKvRg0I", "Issuer": "ArchivingSystemApp", "Audience": "ArchivingSystem users", "ExpireyMinutes": 30}, "SwaggerConfig": {"EndPoint": "/swagger/v1/swagger.json", "Title": "Docman API", "Version": "v1"}}