using Docman.Core.Application.Common.DTOs.OrganizationalStructure;
using Docman.Core.Application.Features.OrganizationalStructure.Commands.CreateOrganizationalNode;
using Docman.Core.Application.Features.OrganizationalStructure.Commands.UpdateOrganizationalNode;
using Docman.Core.Application.Features.OrganizationalStructure.Commands.DeleteOrganizationalNode;
using Docman.Core.Application.Features.OrganizationalStructure.Queries.GetOrganizationalTree;
using Docman.Core.Application.Features.OrganizationalStructure.Queries.GetOrganizationalNodeByCode;
using Docman.Core.Application.Features.OrganizationalStructure.Queries.GetNodesByLevel;
using Docman.Core.Application.Features.OrganizationalStructure.Queries.GetPathToRoot;
using Mapster;

namespace Docman.Presentation.API.Controllers;

/// <summary>
/// Manage your organization chart and departments hierarchy.
/// Create, update, delete and view organizational units like departments, branches, and sub-units.
/// </summary>
/// <remarks>
/// 🔐 All endpoints require authentication (Bearer token).
/// 📋 Each organizational unit has a unique 5-character code (like "ABC12").
/// 🏢 Structure: Central Office → Departments/Branches → Units → Sub-Units
/// </remarks>
[SwaggerResponse(200, "Operation completed successfully")]
[SwaggerResponse(400, "Invalid operation or input data")]
[SwaggerResponse(401, "User is not authorized to perform this operation")]
[SwaggerResponse(404, "Organizational node not found")]
[SwaggerResponse(409, "Conflict - operation would violate business rules")]
[Authorize]
public class OrganizationalStructureController(IMediator mediator) : BaseController
{
    /// <summary>
    /// Get the complete organization chart as a tree structure.
    /// </summary>
    /// <returns>
    /// Complete organization chart with all departments, branches, and units in a tree format.
    /// </returns>
    [HttpGet("tree"), SwaggerOperation(
         Summary = "Get organization chart",
         Description =
             "**Returns the complete organization chart as a tree structure." +
             " Perfect for displaying org charts in your frontend." +
             "**<br/><br/>📊 **Use this to:** Build org chart components," +
             " show company structure, display department hierarchies")]
    [ProducesResponseType(typeof(OrganizationalTreeDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetOrganizationalTree()
    {
        var query = new GetOrganizationalTreeQuery();
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Get details of a specific department/unit by its code.
    /// </summary>
    /// <param name="code">The unique 5-character code of the department/unit (e.g., "ABC12")</param>
    /// <param name="includeChildren">Set to true to also get all sub-departments under this unit</param>
    /// <returns>
    /// Details of the requested department/unit, optionally with its sub-departments.
    /// </returns>
    [HttpGet("{code}"), SwaggerOperation(
         Summary = "Get department/unit details",
         Description =
             "**Get detailed information about a specific department or unit." +
             "**<br/><br/>💡 **Examples:**<br/>• GET `/ABC12` - Get details of department ABC12<br/>•" +
             " GET `/ABC12?includeChildren=true` - Get department ABC12 with all its sub-departments" +
             "<br/><br/>🎯 **Use this to:** Show department details, build breadcrumbs, display unit information")]
    [ProducesResponseType(typeof(OrganizationalNodeDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetOrganizationalNodeByCode(
        string code,
        [FromQuery] bool includeChildren = false)
    {
        var query = new GetOrganizationalNodeByCodeQuery(code, includeChildren);
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Get all departments/units at a specific level in the organization.
    /// </summary>
    /// <param name="level">Which level to get: L1 (Head Office), L2 (Main Departments), L3 (Units), L4 (Sub-Units)</param>
    /// <returns>
    /// List of all departments/units at the specified organizational level.
    /// </returns>
    [HttpGet("levels/{level}"), SwaggerOperation(
         Summary = "Get departments by level",
         Description =
             "**Get all departments/units at a specific organizational level.**<br/><br/>📋 **Levels:**<br/>• **L1** - Head Office/Central Administration<br/>• **L2** - Main Departments & Branches<br/>• **L3** - Units & Teams<br/>• **L4** - Sub-Units & Groups<br/><br/>🎯 **Use this to:** Build navigation menus, filter by department level, show departments by hierarchy")]
    [ProducesResponseType(typeof(List<OrganizationalNodeDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetNodesByLevel(
        OrganizationalLevel level)
    {
        var query = new GetNodesByLevelQuery(level);
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Get the full path from head office down to a specific department/unit.
    /// </summary>
    /// <param name="code">The 5-character code of the department/unit you want the path to</param>
    /// <returns>
    /// Array showing the full path from head office to the specified department/unit.
    /// </returns>
    [HttpGet("{code}/path-to-root"), SwaggerOperation(
         Summary = "Get department path (breadcrumbs)",
         Description =
             "**Get the full organizational path to a department - perfect for breadcrumbs!**<br/><br/>💡 **Example:** For unit 'XYZ99', returns: [Head Office → IT Department → Development Unit → XYZ99]<br/><br/>🎯 **Use this to:** Build breadcrumb navigation, show department hierarchy, display organizational path")]
    [ProducesResponseType(typeof(List<OrganizationalNodeDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetPathToRoot(string code)
    {
        var query = new GetPathToRootQuery(code);
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Create a new department, unit, or sub-unit in the organization.
    /// </summary>
    /// <param name="request">Details of the new department/unit to create (name, description, parent, etc.)</param>
    /// <returns>
    /// The newly created department/unit with its auto-generated unique code.
    /// </returns>
    [HttpPost, SwaggerOperation(
         Summary = "Create new department/unit",
         Description =
             "**Create a new department, branch, unit, or sub-unit.**<br/><br/>✨ **What happens:**<br/>• System automatically generates a unique 5-character code<br/>• New unit is placed in the correct hierarchy level<br/>• Returns the complete unit details<br/><br/>📝 **Required fields:** Name, parent department code (if not head office level)<br/><br/>🎯 **Use this to:** Add new departments, create organizational units, expand company structure")]
    [ProducesResponseType(typeof(OrganizationalNodeDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<IActionResult> CreateOrganizationalNode([FromBody] CreateOrganizationalNodeRequest request)
    {
        var command = request.Adapt<CreateOrganizationalNodeCommand>();

        var result = await mediator.Send(command);

        if (result.IsSuccess)
        {
            return CreatedAtAction(
                nameof(GetOrganizationalNodeByCode),
                new { code = result.Value.Code },
                result.Value);
        }

        return result.ToActionResult();
    }

    /// <summary>
    /// Update department/unit information (name, description).
    /// </summary>
    /// <param name="code">The 5-character code of the department/unit to update</param>
    /// <param name="request">New name and description for the department/unit</param>
    /// <returns>
    /// The updated department/unit information.
    /// </returns>
    /// <remarks>
    /// ⚠️ Note: You cannot change the department code - only name and description can be updated.
    /// </remarks>
    [HttpPut("{code}"), SwaggerOperation(
         Summary = "Update department/unit info",
         Description =
             "**Update a department or unit's name and description.**<br/><br/>✏️ **What you can change:**<br/>• Department/unit name<br/>• Description<br/><br/>🚫 **What you cannot change:**<br/>• Department code (to keep hierarchy intact)<br/>• Parent department<br/>• Organizational level<br/><br/>🎯 **Use this to:** Rename departments, update descriptions, modify unit information")]
    [ProducesResponseType(typeof(OrganizationalNodeDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<IActionResult> UpdateOrganizationalNode(
        string code,
        [FromBody] UpdateOrganizationalNodeRequest request)
    {
        var command = new UpdateOrganizationalNodeCommand(
            code,
            request.Name,
            request.Description);
        var result = await mediator.Send(command);

        return result.ToActionResult();
    }

    /// <summary>
    /// Delete a department/unit from the organization.
    /// </summary>
    /// <param name="code">The 5-character code of the department/unit to delete</param>
    /// <param name="cascadeDelete">Set to true to also delete all sub-departments under this unit (be careful!)</param>
    /// <returns>
    /// Success confirmation or error if deletion failed.
    /// </returns>
    [HttpDelete("{code}"), SwaggerOperation(
         Summary = "Delete department/unit",
         Description =
             "**Delete a department or unit from the organization.**<br/><br/>⚠️ **Important:**<br/>• **cascadeDelete=false** (default): Only deletes if no sub-departments exist<br/>• **cascadeDelete=true**: Deletes the department AND all its sub-departments (dangerous!)<br/><br/>💡 **Examples:**<br/>• DELETE `/ABC12` - Delete department ABC12 (only if empty)<br/>• DELETE `/ABC12?cascadeDelete=true` - Delete ABC12 and ALL its sub-departments<br/><br/>🎯 **Use this to:** Remove empty departments, reorganize structure, clean up unused units")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<IActionResult> DeleteOrganizationalNode(
        string code,
        [FromQuery] bool cascadeDelete = false)
    {
        var command = new DeleteOrganizationalNodeCommand(code, cascadeDelete);
        var result = await mediator.Send(command);

        return result.IsSuccess ? NoContent() : result.ToActionResult();
    }
}