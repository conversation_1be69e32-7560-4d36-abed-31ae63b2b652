using Docman.Core.Application.Common.DTOs.Stocks;

namespace Docman.Core.Application.Features.Stocks.Queries.GetAllStocks;

/// <summary>
/// Handler for GetAllStocksQuery
/// </summary>
public sealed class GetAllStocksQueryHandler(
    IGenericRepository<Stock> stockRepository
) : IRequestHandler<GetAllStocksQuery, Result<IEnumerable<StockListDto>>>
{
    public async Task<Result<IEnumerable<StockListDto>>> Handle(GetAllStocksQuery request, CancellationToken cancellationToken)
    {
        // Get all stocks with related location data
        var stocks = await stockRepository.GetAllAsync(
            filter: s => !s.IsDeleted,
            includeProperties: ["Village.CityOrDistrict.Governorate.Country"],
            cancellationToken: cancellationToken);

        // Map to DTOs using Mapster
        var stockDtos = stocks
            .OrderBy(s => s.Name)
            .Adapt<List<StockListDto>>();

        return Result.Success<IEnumerable<StockListDto>>(stockDtos);
    }
}
