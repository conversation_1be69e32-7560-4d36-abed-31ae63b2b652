﻿

using Docman.Core.Application.Contracts;
using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.Users
{
    public sealed class ChangePasswordCommandHandler(IUserRepository userRepository)
        :IRequestHandler<ChangePasswordCommand,Result>
    {
        private readonly IUserRepository _userRepository = userRepository;

        public async Task<Result> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
        {
            return await _userRepository.ChangePasswordAsync(request.UserId, request.CurrentPassword, request.NewPassword, cancellationToken);
        }
    }
}
