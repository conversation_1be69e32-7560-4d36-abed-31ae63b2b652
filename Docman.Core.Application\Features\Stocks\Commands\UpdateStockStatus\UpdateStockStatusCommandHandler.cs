using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Stocks.Commands.UpdateStockStatus;

/// <summary>
/// Handler for UpdateStockStatusCommand
/// </summary>
public sealed class UpdateStockStatusCommandHandler(
    IGenericRepository<Stock> stockRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<UpdateStockStatusCommand, Result>
{
    public async Task<Result> Handle(UpdateStockStatusCommand request, CancellationToken cancellationToken)
    {
        // Get the existing stock
        var existingStock = await stockRepository.GetAsync(
            filter: s => s.Id == request.Id && !s.IsDeleted,
            cancellationToken: cancellationToken);

        if (existingStock == null)
        {
            return Result.Failure(StockErrors.StockNotFound);
        }

        // Update the status
        existingStock.Status = request.NewStatus;

        // Save changes
        await stockRepository.UpdateAsync(existingStock, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success($"Stock status updated to {request.NewStatus} successfully.");
    }
    
}
