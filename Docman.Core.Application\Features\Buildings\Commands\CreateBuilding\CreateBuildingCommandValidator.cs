namespace Docman.Core.Application.Features.Buildings.Commands.CreateBuilding;

/// <summary>
/// Validator for CreateBuildingCommand
/// </summary>
public sealed class CreateBuildingCommandValidator : AbstractValidator<CreateBuildingCommand>
{
    public CreateBuildingCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Building name is required")
            .MaximumLength(200)
            .WithMessage("Building name cannot exceed 200 characters");

        RuleFor(x => x.Address)
            .NotEmpty()
            .WithMessage("Building address is required")
            .MaximumLength(500)
            .WithMessage("Building address cannot exceed 500 characters");

        RuleFor(x => x.DistrictOrVillageCode)
            .NotEmpty()
            .WithMessage("District or village code is required")
            .MaximumLength(10)
            .WithMessage("District or village code cannot exceed 10 characters");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("Invalid email format")
            .MaximumLength(100)
            .WithMessage("Email cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.MobileNumber)
            .Matches(@"^[\+]?[0-9\-\(\)\s]+$")
            .WithMessage("Invalid mobile number format")
            .MaximumLength(20)
            .WithMessage("Mobile number cannot exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.MobileNumber));
    }
}
