using Docman.Core.Application.Common.DTOs.Buildings;
using Docman.Core.Application.Common.Errors;


namespace Docman.Core.Application.Features.Buildings.Queries.GetBuildingById;

/// <summary>
/// Handler for GetBuildingByIdQuery
/// </summary>
public sealed class GetBuildingByIdQueryHandler(
    IGenericRepository<Building> buildingRepository
) : IRequestHandler<GetBuildingByIdQuery, Result<BuildingDetailsDto>>
{
    public async Task<Result<BuildingDetailsDto>> Handle(GetBuildingByIdQuery request,
        CancellationToken cancellationToken)
    {
        // Get building with related location data
        var building = await buildingRepository.GetAsync(
            filter: b => b.Id == request.Id,
            includeProperties: ["DistrictOrVillage.CityOrDistrict.Governorate.Country"],
            cancellationToken: cancellationToken
        );

        if (building == null || building.IsDeleted)
        {
            return Result.Failure<BuildingDetailsDto>(BuildingErrors.BuildingNotFound);
        }

        // Map to detailed DTO using Mapster
        var buildingDto = building.Adapt<BuildingDetailsDto>();

        return Result.Success(buildingDto);
    }
}