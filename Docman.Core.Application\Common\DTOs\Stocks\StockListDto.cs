using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Common.DTOs.Stocks;

/// <summary>
/// DTO for stock list view containing essential information for listing
/// </summary>
public sealed record StockListDto
{
    /// <summary>
    /// Unique identifier of the stock
    /// </summary>
    public int Id { get; init; }
    
    /// <summary>
    /// Name of the stock
    /// </summary>
    public string Name { get; init; } = string.Empty;
    
    /// <summary>
    /// Arabic name of the village where the stock is located
    /// </summary>
    public string VillageNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// Arabic name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the country derived from governorate location
    /// </summary>
    public string CountryNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the country derived from governorate location
    /// </summary>
    public string CountryNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Current status of the stock
    /// </summary>
    public StockStatus Status { get; init; }

    /// <summary>
    /// Name of the person responsible for the stock
    /// </summary>
    public string ManagerName { get; init; } = string.Empty;
}
