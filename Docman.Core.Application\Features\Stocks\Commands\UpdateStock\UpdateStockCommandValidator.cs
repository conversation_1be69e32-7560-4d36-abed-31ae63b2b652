using Docman.Core.Application.Common.Constants;
using Docman.Core.Application.Features.Stocks.Commands.CreateStock;

namespace Docman.Core.Application.Features.Stocks.Commands.UpdateStock;

/// <summary>
/// Validator for UpdateStockCommand
/// </summary>
public sealed class UpdateStockCommandValidator : AbstractValidator<UpdateStockCommand>
{
    public UpdateStockCommandValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Stock ID must be greater than 0");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Stock name is required")
            .MaximumLength(200)
            .WithMessage("Stock name cannot exceed 200 characters");

        RuleFor(x => x.ManagerName)
            .NotEmpty()
            .WithMessage("Manager name is required")
            .MaximumLength(200)
            .WithMessage("Manager name cannot exceed 200 characters");

        RuleFor(x => x.VillageCode)
            .NotEmpty()
            .WithMessage("Village code is required")
            .MaximumLength(10)
            .WithMessage("Village code cannot exceed 10 characters");

        RuleFor(x => x.Address)
            .NotEmpty()
            .WithMessage("Stock address is required")
            .MaximumLength(500)
            .WithMessage("Stock address cannot exceed 500 characters");

        RuleFor(x => x.MaintenancePhone)
            .Matches(RegexPattern.PhoneNumber)
            .WithMessage("Invalid maintenance phone number format")
            .MaximumLength(20)
            .WithMessage("Maintenance phone number cannot exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.MaintenancePhone));

        RuleFor(x => x.SecurityPhone)
            .Matches(RegexPattern.PhoneNumber)
            .WithMessage("Invalid security phone number format")
            .MaximumLength(20)
            .WithMessage("Security phone number cannot exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.SecurityPhone));

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("Invalid email format")
            .MaximumLength(100)
            .WithMessage("Email cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        // Validate stock phones collection
        RuleForEach(x => x.StockPhones)
            .SetValidator(new CreateStockPhoneRequestValidator())
            .When(x => x.StockPhones?.Any() == true);
    }
}
