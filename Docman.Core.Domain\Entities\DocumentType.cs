namespace Docman.Core.Domain.Entities;

/// <summary>
/// Represents a document type in the system for classifying documents
/// </summary>
public class DocumentType
{
    /// <summary>
    /// Unique identifier of the document type (Primary Key)
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Name of the document type (must be unique)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the document type (default: Unapproved)
    /// </summary>
    public DocumentTypeStatus Status { get; set; } = DocumentTypeStatus.Unapproved;
}
