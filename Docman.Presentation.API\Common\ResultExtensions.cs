using Docman.Core.Application.Common.Results;

namespace Docman.Presentation.API.Common;

public static class ResultExtensions
{
    public static IActionResult ToActionResult<TValue>(this Result<TValue> result,
        int successStatusCode = StatusCodes.Status200OK,
        int errorStatusCode = StatusCodes.Status400BadRequest)
    {
        if (result.IsSuccess)
        {
            return successStatusCode switch
            {
                StatusCodes.Status200OK => new OkObjectResult(result.Value),
                StatusCodes.Status201Created => new ObjectResult(result.Value) { StatusCode = StatusCodes.Status201Created },
                StatusCodes.Status204NoContent => new NoContentResult(),
                _ => new ObjectResult(result.Value) { StatusCode = successStatusCode }
            };
        }
        
        return CreateErrorResult(result.Error!, errorStatusCode);
    }

    public static IActionResult ToActionResult(
        this Result result,
        int successStatusCode = StatusCodes.Status200OK,
        int errorStatusCode = StatusCodes.Status400BadRequest)
    {
        if (result.IsSuccess)
        {
            return successStatusCode switch
            {
                StatusCodes.Status200OK => new OkResult(),
                StatusCodes.Status204NoContent => new NoContentResult(),
                _ => new StatusCodeResult(successStatusCode)
            };
        }
        
        return CreateErrorResult(result.Error!, errorStatusCode);
    }

    private static ObjectResult CreateErrorResult(Error error, int statusCode)
    {
        var problem = Results.Problem(statusCode: statusCode);

        var problemDetails = problem.GetType().GetProperty(nameof(ProblemDetails))?.GetValue(problem) as ProblemDetails;

        problemDetails!.Extensions = new Dictionary<string, object?>
        {
            { "errors", new[] { error } }
        };

        return new ObjectResult(problemDetails);
    }
}
