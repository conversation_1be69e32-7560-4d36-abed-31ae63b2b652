using Docman.Core.Application.Common.DTOs.Stocks;
using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Stocks.Queries.GetStockDetails;

/// <summary>
/// Handler for GetStockDetailsQuery
/// </summary>
public sealed class GetStockDetailsQueryHandler(
    IGenericRepository<Stock> stockRepository
) : IRequestHandler<GetStockDetailsQuery, Result<StockDetailsDto>>
{
    public async Task<Result<StockDetailsDto>> Handle(GetStockDetailsQuery request, CancellationToken cancellationToken)
    {
        // Get stock with complete details including location and phone numbers
        var stock = await stockRepository.GetAsync(
            filter: s => s.Id == request.Id && !s.IsDeleted,
            includeProperties: ["Village.CityOrDistrict.Governorate.Country", "StockPhones"],
            cancellationToken: cancellationToken);

        if (stock == null)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.StockNotFound);
        }

        // Map to detailed DTO using Mapster
        var stockDto = stock.Adapt<StockDetailsDto>();

        return Result.Success(stockDto);
    }
}
