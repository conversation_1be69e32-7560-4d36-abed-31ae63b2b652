namespace Docman.Core.Domain.Entities;

public class Stock : AuditableEntity<int>
{
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Name of the person responsible for the stock (required)
    /// </summary>
    public string ManagerName { get; set; } = string.Empty;

    /// <summary>
    /// Foreign key to the Village table (required)
    /// </summary>
    public string VillageCode { get; set; } = string.Empty;

    /// <summary>
    /// Detailed address of the stock (required)
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// Phone number for maintenance (optional)
    /// </summary>
    public string? MaintenancePhone { get; set; }

    /// <summary>
    /// Phone number for security (optional)
    /// </summary>
    public string? SecurityPhone { get; set; }

    /// <summary>
    /// Email address for the stock (optional)
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Additional details about the stock (optional)
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Current status of the stock (default: Unapproved)
    /// </summary>
    public StockStatus Status { get; set; } = StockStatus.Unapproved;

    public DistrictOrVillage Village { get; set; } = null!;

    /// <summary>
    /// Navigation property to associated phone numbers
    /// </summary>
    public ICollection<StockPhone> StockPhones { get; set; } = new List<StockPhone>();
}