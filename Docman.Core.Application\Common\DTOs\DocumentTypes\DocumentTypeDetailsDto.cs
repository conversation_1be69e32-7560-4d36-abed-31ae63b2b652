using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Common.DTOs.DocumentTypes;

/// <summary>
/// DTO for detailed document type information including all properties
/// </summary>
public sealed record DocumentTypeDetailsDto
{
    /// <summary>
    /// Unique identifier of the document type
    /// </summary>
    public int Id { get; init; }

    /// <summary>
    /// Name of the document type
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// Current status of the document type
    /// </summary>
    public DocumentTypeStatus Status { get; init; }
}

