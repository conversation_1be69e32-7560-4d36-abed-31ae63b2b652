namespace Docman.Core.Application.Common.DTOs.Stocks;

/// <summary>
/// Request model for creating a new stock
/// </summary>
public class CreateStockRequest
{
    /// <summary>
    /// Name of the stock (required)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Name of the person responsible for the stock (required)
    /// </summary>
    public string ManagerName { get; set; } = string.Empty;

    /// <summary>
    /// Village code where the stock is located (required)
    /// </summary>
    public string VillageCode { get; set; } = string.Empty;

    /// <summary>
    /// Detailed address of the stock (required)
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// Phone number for maintenance (optional)
    /// </summary>
    public string? MaintenancePhone { get; set; }

    /// <summary>
    /// Phone number for security (optional)
    /// </summary>
    public string? SecurityPhone { get; set; }

    /// <summary>
    /// Email address for the stock (optional)
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Additional details about the stock (optional)
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Associated phone numbers (optional)
    /// </summary>
    public ICollection<CreateStockPhoneRequest> StockPhones { get; set; } = new List<CreateStockPhoneRequest>();
}
