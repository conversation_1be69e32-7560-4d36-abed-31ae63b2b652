using Docman.Core.Application.Common.Errors;
using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Features.Buildings.Commands.UpdateBuilding;

/// <summary>
/// Handler for UpdateBuildingCommand
/// </summary>
public sealed class UpdateBuildingCommandHandler(
    IGenericRepository<Building> buildingRepository,
    IGenericRepository<DistrictOrVillage> districtOrVillageRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<UpdateBuildingCommand, Result<bool>>
{
    public async Task<Result<bool>> Handle(UpdateBuildingCommand request, CancellationToken cancellationToken)
    {
        // Get the existing building
        var building = await buildingRepository.GetAsync(
            filter: b => b.Id == request.Id,
            cancellationToken: cancellationToken);

        if (building == null)
            return Result.Failure<bool>(BuildingErrors.BuildingNotFound);

        // Verify that the district or village exists
        var districtOrVillage = await districtOrVillageRepository.GetAsync(
            filter: d => d.Code == request.DistrictOrVillageCode,
            cancellationToken: cancellationToken);

        if (districtOrVillage == null)
            return Result.Failure<bool>(BuildingErrors.DistrictOrVillageNotFound);


        // Check if any data has changed to determine if status should be reset
        var buildingDataChanged = HasBuildingDataChanged(building, request);

        building = request.Adapt<Building>();

        // Handle status logic as per requirements
        if (request.Status.HasValue)
        {
            // If status is explicitly provided, use it
            building.Status = request.Status.Value;
        }
        else if (buildingDataChanged)
        {
            // If data changed but no status provided, reset to Unapproved
            building.Status = BuildingStatus.Unapproved;
        }
        // If no data changed and no status provided, keep existing status

        // Update the repository
        await buildingRepository.UpdateAsync(building, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(true);
    }

    private static bool HasBuildingDataChanged(Building building, UpdateBuildingCommand request)
    {
        return building.Name != request.Name ||
               building.Address != request.Address ||
               building.Description != request.Description ||
               building.Email != request.Email ||
               building.MobileNumber != request.MobileNumber ||
               building.DistrictOrVillageCode != request.DistrictOrVillageCode;
    }
}