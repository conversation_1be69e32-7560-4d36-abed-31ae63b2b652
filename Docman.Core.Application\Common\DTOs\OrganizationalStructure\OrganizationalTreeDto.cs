namespace Docman.Core.Application.Common.DTOs.OrganizationalStructure;

/// <summary>
/// Represents the complete organizational tree structure
/// </summary>
public class OrganizationalTreeDto
{
    /// <summary>
    /// Root nodes (L1 level)
    /// </summary>
    public List<OrganizationalTreeNodeDto> RootNodes { get; set; } = new();

    /// <summary>
    /// Total count of nodes at each level
    /// </summary>
    public Dictionary<OrganizationalLevel, int> LevelCounts { get; set; } = new();

    /// <summary>
    /// Total number of nodes in the tree
    /// </summary>
    public int TotalNodes { get; set; }
}

/// <summary>
/// Represents a node in the organizational tree with its children
/// </summary>
public class OrganizationalTreeNodeDto
{
    /// <summary>
    /// 5-character Base-36 code
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Display name of the node
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Organizational level (L1-L4)
    /// </summary>
    public OrganizationalLevel Level { get; set; }



    /// <summary>
    /// Child nodes
    /// </summary>
    public List<OrganizationalTreeNodeDto> Children { get; set; } = new();

    /// <summary>
    /// Count of direct children
    /// </summary>
    public int ChildrenCount => Children.Count;
}
