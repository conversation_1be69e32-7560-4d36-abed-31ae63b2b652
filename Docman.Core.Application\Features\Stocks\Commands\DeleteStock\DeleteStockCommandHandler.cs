using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Stocks.Commands.DeleteStock;

/// <summary>
/// Handler for DeleteStockCommand
/// </summary>
public sealed class DeleteStockCommandHandler(
    IGenericRepository<Stock> stockRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<DeleteStockCommand, Result>
{
    public async Task<Result> Handle(DeleteStockCommand request, CancellationToken cancellationToken)
    {
        // Get the existing stock
        var existingStock = await stockRepository.GetAsync(
            filter: s => s.Id == request.Id && !s.IsDeleted,
            cancellationToken: cancellationToken);
        if (existingStock == null)
        {
            return Result.Failure(StockErrors.StockNotFound);
        }

        // Perform soft delete
        await stockRepository.DeleteAsync(existingStock, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success($"Stock with ID '{request.Id}' deleted successfully.");
    }
}
