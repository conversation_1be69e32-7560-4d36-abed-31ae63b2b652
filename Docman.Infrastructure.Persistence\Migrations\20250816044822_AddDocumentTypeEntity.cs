﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Docman.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddDocumentTypeEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DocumentTypes",
                columns: table => new
                {
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "Unique name of the document type (Primary Key)"),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 0, comment: "Current status of the document type (0=Unapproved, 1=Approved, 2=Frozen)")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocumentTypes", x => x.Name);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DocumentTypes_Status",
                table: "DocumentTypes",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DocumentTypes");
        }
    }
}
