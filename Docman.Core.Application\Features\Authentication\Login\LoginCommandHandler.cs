﻿

using Docman.Core.Application.Common.DTOs;
using Docman.Core.Application.Contracts;
using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.Authentication.Login;

public sealed class LoginCommandHandler(IAuthRepository authRepository) : IRequestHandler<LoginCommand,Result<AuthResponse>>
{
    private readonly IAuthRepository _authRepository = authRepository;

    public async Task<Result<AuthResponse>> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        return await _authRepository.GetTokenAsync(request.Email,request.Password,cancellationToken);
    }
}
