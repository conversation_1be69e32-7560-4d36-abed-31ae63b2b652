namespace Docman.Core.Application.Common.Errors;

/// <summary>
/// Contains error definitions specific to Building operations
/// </summary>
public static class BuildingErrors
{
    public static readonly Error BuildingNotFound = new("Building.NotFound", "The specified building was not found.");
    
    public static readonly Error DistrictOrVillageNotFound = new("Building.DistrictOrVillageNotFound", "The specified district or village was not found.");
    
    public static readonly Error InvalidBuildingData = new("Building.InvalidData", "The provided building data is invalid.");
    
    public static readonly Error BuildingAlreadyExists = new("Building.AlreadyExists", "A building with the same name already exists in this location.");
}
