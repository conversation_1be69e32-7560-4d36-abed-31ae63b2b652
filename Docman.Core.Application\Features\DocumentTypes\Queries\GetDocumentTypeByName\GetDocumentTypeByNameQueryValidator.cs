namespace Docman.Core.Application.Features.DocumentTypes.Queries.GetDocumentTypeByName;

/// <summary>
/// Validator for GetDocumentTypeByNameQuery
/// </summary>
public sealed class GetDocumentTypeByNameQueryValidator : AbstractValidator<GetDocumentTypeByNameQuery>
{
    public GetDocumentTypeByNameQueryValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Document type name is required.");
    }
}
