using Docman.Core.Application.Features.Locations.Queries.GetAllCountries;
using Docman.Core.Application.Features.Locations.Queries.GetGovernoratesByCountryQuery;
using Docman.Core.Application.Features.Locations.Queries.GetCitiesByGovernorateQuery;
using Docman.Core.Application.Features.Locations.Queries.GetDistrictsByCityQuery;
using Docman.Presentation.API.Controllers.Base;
using Docman.Presentation.API.Common;

namespace Docman.Presentation.API.Controllers;

/// <summary>
/// Get location data for forms and dropdowns - countries, states, cities, and districts.
/// </summary>
/// <remarks>
/// 🌍 **Perfect for:** Address forms, location dropdowns, geographic selectors.
/// 📍 **Hierarchy:** Country → Governorate/State → City → District/Village
/// 🔓 **No authentication required** - public geographic data.
/// </remarks>
[SwaggerResponse(200, "Operation completed successfully")]
[SwaggerResponse(400, "Invalid request data")]
[SwaggerResponse(404, "Location not found")]
public class LocationController(IMediator mediator) : BaseController
{
    /// <summary>
    /// Get list of all countries for country selection dropdown.
    /// </summary>
    /// <returns>
    /// List of all countries with names and codes.
    /// </returns>
    [HttpGet("countries"), SwaggerOperation(
        Summary = "Get all countries",
        Description = "**Get complete list of countries for dropdown menus.**<br/><br/>🌍 **What you get:**<br/>• Country names (in multiple languages if available)<br/>• Country codes for API calls<br/>• All countries in the system<br/><br/>💡 **Perfect for:**<br/>• Country selection dropdowns<br/>• Address forms<br/>• User registration forms<br/><br/>🎯 **Use this to:** Build country selectors, populate address forms, geographic filters")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetAllCountries()
    {
        var query = new GetAllCountriesQuery();
        var result = await mediator.Send(query);
        
        return result.ToActionResult();
    }

    /// <summary>
    /// Get states/governorates for a specific country.
    /// </summary>
    /// <param name="countryCode">The country code to get states/governorates for</param>
    /// <returns>
    /// List of states/governorates in the specified country.
    /// </returns>
    [HttpGet("countries/{countryCode}/governorates"), SwaggerOperation(
        Summary = "Get states/governorates by country",
        Description = "**Get states or governorates within a specific country.**<br/><br/>📍 **How to use:**<br/>1. First get countries from `/countries`<br/>2. Use a country code to get its states/governorates<br/>3. Use for cascading dropdowns<br/><br/>💡 **Example:** GET `/countries/EG/governorates` - Get Egyptian governorates<br/><br/>🎯 **Use this to:** State/province dropdowns, address forms, location hierarchies")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetGovernoratesByCountry(string countryCode)
    {
        var query = new GetGovernoratesByCountryQuery(countryCode);
        var result = await mediator.Send(query);
        
        return result.ToActionResult();
    }

    /// <summary>
    /// Get cities within a specific state/governorate.
    /// </summary>
    /// <param name="govCode">The governorate/state code to get cities for</param>
    /// <returns>
    /// List of cities within the specified state/governorate.
    /// </returns>
    [HttpGet("governorates/{govCode}/cities"), SwaggerOperation(
        Summary = "Get cities by state/governorate",
        Description = "**Get cities within a specific state or governorate.**<br/><br/>📍 **How to use:**<br/>1. Get countries → states → cities (in that order)<br/>2. Use governorate code from previous call<br/>3. Perfect for cascading location dropdowns<br/><br/>💡 **Example:** GET `/governorates/CAI/cities` - Get cities in Cairo governorate<br/><br/>🎯 **Use this to:** City dropdowns, address forms, location-based services")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCitiesByGovernorate(string govCode)
    {
        var query = new GetCitiesByGovernorateQuery(govCode);
        var result = await mediator.Send(query);
        
        return result.ToActionResult();
    }

    /// <summary>
    /// Get districts/neighborhoods within a specific city.
    /// </summary>
    /// <param name="cityCode">The city code to get districts/neighborhoods for</param>
    /// <returns>
    /// List of districts, neighborhoods, or villages within the specified city.
    /// </returns>
    [HttpGet("cities/{cityCode}/districts"), SwaggerOperation(
        Summary = "Get districts/neighborhoods by city",
        Description = "**Get districts, neighborhoods, or villages within a specific city.**<br/><br/>📍 **Final level of hierarchy:**<br/>Country → State → City → **District/Neighborhood**<br/><br/>📍 **Complete flow:**<br/>1. Countries → 2. States → 3. Cities → 4. Districts<br/>2. Each step uses the code from the previous step<br/><br/>💡 **Example:** GET `/cities/NYC/districts` - Get neighborhoods in New York City<br/><br/>🎯 **Use this to:** Detailed address forms, neighborhood selectors, precise location selection")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDistrictsByCity(string cityCode)
    {
        var query = new GetDistrictsByCityQuery(cityCode);
        var result = await mediator.Send(query);
        
        return result.ToActionResult();
    }
}
