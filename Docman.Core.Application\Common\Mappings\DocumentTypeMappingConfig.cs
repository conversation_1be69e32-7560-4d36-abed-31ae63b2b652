using Docman.Core.Application.Common.DTOs.DocumentTypes;
using Docman.Core.Application.Features.DocumentTypes.Commands.CreateDocumentType;

namespace Docman.Core.Application.Common.Mappings;

/// <summary>
/// Mapster configuration for DocumentType mappings
/// </summary>
public class DocumentTypeMappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // Request DTO to Command mappings
        config.NewConfig<CreateDocumentTypeRequest, CreateDocumentTypeCommand>()
            .Map(dest => dest.Name, src => src.Name);

        // Command to Entity mappings
        config.NewConfig<CreateDocumentTypeCommand, DocumentType>()
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Status, src => DocumentTypeStatus.Unapproved); // Always start as Unapproved

        // Entity to DTO mappings
        config.NewConfig<DocumentType, DocumentTypeListDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Status, src => src.Status);

        config.NewConfig<DocumentType, DocumentTypeDetailsDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Status, src => src.Status);
    }
}
