﻿using System.Security.Cryptography;
using Docman.Core.Application.Common.Authentication;
using Docman.Core.Application.Common.DTOs;
using Docman.Core.Application.Common.Errors;
using Docman.Core.Application.Common.Results;

namespace Docman.Infrastructure.Persistence.Repositories.Specifications;

public class AuthRepository(
    UserManager<ApplicationUser> userManager
   , IJwtProvider jwtProvider
   , SignInManager<ApplicationUser> signInManager,
    DocmanDbContext context) : IAuthRepository
{
    private readonly UserManager<ApplicationUser> _userManager = userManager;
    private readonly IJwtProvider _jwtProvider = jwtProvider;
    private readonly SignInManager<ApplicationUser> _signInManager = signInManager;
    private readonly DocmanDbContext _context = context;
    private readonly int _refreshTokenExpiryDays = 14;
    public async Task<Result<AuthResponse>> GetTokenAsync(string email, string password, CancellationToken cancellation)
    {
        var user = await _userManager.FindByEmailAsync(email);

        if (user is null)
            return Result.Failure<AuthResponse>(UserErrors.InvalidCredentials);

        //check password
        var result = await _signInManager.PasswordSignInAsync(user, password, false, true);

        if (result.Succeeded)
        {
            //generate token
            var (userRoles, userPermissions) = await GetUserRolesAndPermission(user, cancellation);


            var (token, expiresIn) = _jwtProvider.GenerateToken(user, userRoles, userPermissions);

            var refreshToken = GenerateRefreshToken();

            var refreshTokenExpirationDate = DateTime.UtcNow.AddDays(_refreshTokenExpiryDays);

            user.RefreshTokens.Add(
                new RefreshToken
                {
                    Token = refreshToken,
                    ExpiresOn = refreshTokenExpirationDate
                }
                );

            await _userManager.UpdateAsync(user);

            var response = new AuthResponse(user.Id, user.Email, user.FirstName, user.LastName, token, expiresIn, refreshToken, refreshTokenExpirationDate,user.MustChangePassword);

            return Result.Success(response);
        }

        var error = result.IsLockedOut
               ? UserErrors.LockedOut
               : UserErrors.InvalidCredentials;

        return Result.Failure<AuthResponse>(error);
    }
    public async Task<Result<AuthResponse>> GetRefreshTokenAsync(string token, string refreshToken, CancellationToken cancellation)
    {
        var userId = _jwtProvider.ValidateToken(token);

        if (userId is null)
            return Result.Failure<AuthResponse>(UserErrors.InvalidCredentials);


        var user = await _userManager.FindByIdAsync(userId);

        if (user is null)
            return Result.Failure<AuthResponse>(UserErrors.InvalidCredentials);

        if (user.LockoutEnd > DateTime.UtcNow)
            return Result.Failure<AuthResponse>(UserErrors.LockedOut);

        var userRefreshToken = user.RefreshTokens.SingleOrDefault(x => x.Token == refreshToken && x.IsActive);

        if (userRefreshToken is null)
            return Result.Failure<AuthResponse>(UserErrors.InvalidRefreshToken);


        userRefreshToken.RevokedOn = DateTime.UtcNow;

        var (userRoles, userPermissions) = await GetUserRolesAndPermission(user, cancellation);

        var (newToken, expiresIn) = _jwtProvider.GenerateToken(user, userRoles, userPermissions);

        var newRefreshToken = GenerateRefreshToken();

        var refreshTokenExpirationDate = DateTime.UtcNow.AddDays(_refreshTokenExpiryDays);

        user.RefreshTokens.Add(
            new RefreshToken
            {
                Token = newRefreshToken,
                ExpiresOn = refreshTokenExpirationDate
            }
            );

        await _userManager.UpdateAsync(user);

        var response = new AuthResponse(user.Id, user.Email, user.FirstName, user.LastName, newToken, expiresIn, newRefreshToken, refreshTokenExpirationDate,user.MustChangePassword);

        return Result.Success(response);
    }
    public async Task<bool> RevokeRefreshTokenAsync(string token, string refreshToken, CancellationToken cancellation)
    {
        var userId = _jwtProvider.ValidateToken(token);

        if (userId is null)
            return false;

        var user = await _userManager.FindByIdAsync(userId);

        if (user is null)
            return false;

        var userRefreshToken = user.RefreshTokens.SingleOrDefault(x => x.Token == refreshToken && x.IsActive);

        if (userRefreshToken is null)
            return false;

        userRefreshToken.RevokedOn = DateTime.UtcNow;

        await _userManager.UpdateAsync(user);

        return true;
    }

    private static string GenerateRefreshToken()
    {
        return Convert.ToBase64String(RandomNumberGenerator.GetBytes(64));
    }

    private async Task<(IEnumerable<string> userRoles, IEnumerable<string> userPermission)> GetUserRolesAndPermission(ApplicationUser user, CancellationToken cancellation)
    {
        var userRoles = await _userManager.GetRolesAsync(user);

        var userPermissions = await _context.Roles
            .Join(_context.RoleClaims,
            role => role.Id,
            claim => claim.RoleId,
            (role, claim) => new { role, claim }
            )
            .Where(x => userRoles.Contains(x.role.Name!))
            .Select(x => x.claim.ClaimValue!)
            .Distinct()
            .ToListAsync(cancellation);

        return (userRoles, userPermissions);
    }
}
