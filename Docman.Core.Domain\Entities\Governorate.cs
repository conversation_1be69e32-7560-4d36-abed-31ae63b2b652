﻿namespace Docman.Core.Domain.Entities;

public class Governorate
{
    public String Code { get; set; }
    public String NameAr { get; set; }
    public String NameEn { get; set; }
    public String CountryCode { get; set; }
    
    // Navigation property to Country
    public Country Country { get; set; }
    public ICollection<CityOrDistrict> CityOrDistricts { get; set; } = new List<CityOrDistrict>();
}