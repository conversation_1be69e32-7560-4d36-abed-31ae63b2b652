namespace Docman.Infrastructure.Persistence.Configurations;

/// <summary>
/// EF Core configuration for StockPhone entity
/// </summary>
public class StockPhoneConfiguration : IEntityTypeConfiguration<StockPhone>
{
    public void Configure(EntityTypeBuilder<StockPhone> builder)
    {
        builder.HasKey(sp => sp.Id);

        builder.Property(sp => sp.Id)
            .ValueGeneratedOnAdd();

        builder.Property(sp => sp.StockId)
            .IsRequired();

        builder.Property(sp => sp.PhoneNumber)
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(sp => sp.Notes)
            .HasMaxLength(500);

        builder.ToTable("StockPhones");
    }
}
