using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Common.DTOs.Buildings;

/// <summary>
/// DTO for detailed building information including all properties and audit data
/// </summary>
public sealed record BuildingDetailsDto
{
    /// <summary>
    /// Unique identifier of the building
    /// </summary>
    public int Id { get; init; }

    /// <summary>
    /// Name of the building
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// Detailed address description of the building
    /// </summary>
    public string Address { get; init; } = string.Empty;

    /// <summary>
    /// Optional description of the building
    /// </summary>
    public string? Description { get; init; }

    /// <summary>
    /// Optional email contact for the building
    /// </summary>
    public string? Email { get; init; }

    /// <summary>
    /// Optional mobile number contact for the building
    /// </summary>
    public string? MobileNumber { get; init; }

    /// <summary>
    /// Current status of the building
    /// </summary>
    public BuildingStatus Status { get; init; }

    /// <summary>
    /// Code of the district or village where the building is located
    /// </summary>
    public string DistrictOrVillageCode { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the village where the building is located
    /// </summary>
    public string VillageNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// Arabic name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the country derived from governorate location
    /// </summary>
    public string CountryNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the country derived from governorate location
    /// </summary>
    public string CountryNameEn { get; init; } = string.Empty;

    // Audit properties
    /// <summary>
    /// User ID who created this building
    /// </summary>
    public string? CreatedByID { get; init; }

    /// <summary>
    /// Timestamp when this building was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; init; }

    /// <summary>
    /// User ID who last updated this building
    /// </summary>
    public string? UpdatedByID { get; init; }

    /// <summary>
    /// Timestamp when this building was last updated
    /// </summary>
    public DateTimeOffset? UpdatedAt { get; init; }
}
