namespace Docman.Core.Application.Common.Errors;

/// <summary>
/// Contains error definitions specific to DocumentType operations
/// </summary>
public static class DocumentTypeErrors
{
    public static readonly Error DocumentTypeNotFound = new("DocumentType.NotFound", "The specified document type was not found.");
    
    public static readonly Error DocumentTypeAlreadyExists = new("DocumentType.AlreadyExists", "A document type with this name already exists.");
    
    public static readonly Error InvalidDocumentTypeData = new("DocumentType.InvalidData", "The provided document type data is invalid.");
    
    public static readonly Error InvalidStatusTransition = new("DocumentType.InvalidStatusTransition", "The status transition is not allowed.");
    
    public static readonly Error DocumentTypeNameRequired = new("DocumentType.NameRequired", "Document type name is required and cannot be empty.");
}
