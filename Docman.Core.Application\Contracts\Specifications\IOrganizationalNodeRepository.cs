namespace Docman.Core.Application.Contracts.Specifications;

/// <summary>
/// Repository interface for organizational node operations with hierarchy-specific functionality
/// </summary>
public interface IOrganizationalNodeRepository : IGenericRepository<OrganizationalNode>
{
    /// <summary>
    /// Gets all nodes at a specific organizational level
    /// </summary>
    /// <param name="level">The organizational level</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of nodes at the specified level</returns>
    Task<List<OrganizationalNode>> GetNodesByLevelAsync(
        OrganizationalLevel level, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a node by its code with optional child loading
    /// </summary>
    /// <param name="code">The node code</param>
    /// <param name="includeChildren">Whether to include child nodes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The organizational node or null if not found</returns>
    Task<OrganizationalNode?> GetByCodeAsync(
        string code, 
        bool includeChildren = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all children of a specific node
    /// </summary>
    /// <param name="parentCode">The parent node code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of child nodes</returns>
    Task<List<OrganizationalNode>> GetChildrenAsync(
        string parentCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all descendants of a specific node (complete subtree)
    /// </summary>
    /// <param name="parentCode">The parent node code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all descendant nodes</returns>
    Task<List<OrganizationalNode>> GetDescendantsAsync(
        string parentCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the complete path from a node to the root
    /// </summary>
    /// <param name="code">The node code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of nodes from root to the specified node</returns>
    Task<List<OrganizationalNode>> GetPathToRootAsync(
        string code,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the complete organizational tree
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Complete tree structure</returns>
    Task<List<OrganizationalNode>> GetCompleteTreeAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a node has any children
    /// </summary>
    /// <param name="parentCode">The parent node code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the node has children</returns>
    Task<bool> HasChildrenAsync(
        string parentCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets count of nodes at each level
    /// </summary>

    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary with level counts</returns>
    Task<Dictionary<OrganizationalLevel, int>> GetLevelCountsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all existing codes to avoid duplicates during code generation
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Set of all existing codes</returns>
    Task<HashSet<string>> GetAllExistingCodesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a code is already in use
    /// </summary>
    /// <param name="code">The code to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the code exists</returns>
    Task<bool> CodeExistsAsync(string code, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets nodes by name pattern (for search functionality)
    /// </summary>
    /// <param name="namePattern">The name pattern to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching nodes</returns>
    Task<List<OrganizationalNode>> SearchByNameAsync(
        string namePattern,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates if a parent-child relationship is valid based on hierarchy rules
    /// </summary>
    /// <param name="childCode">The child node code</param>
    /// <param name="parentCode">The parent node code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the relationship is valid</returns>
    Task<bool> ValidateParentChildRelationshipAsync(
        string childCode,
        string? parentCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Hard deletes a node and optionally its descendants
    /// </summary>
    /// <param name="code">The node code to delete</param>
    /// <param name="cascadeDelete">Whether to delete all descendants</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of nodes deleted</returns>
    Task<int> HardDeleteAsync(
        string code,
        bool cascadeDelete = false,
        CancellationToken cancellationToken = default);
}
