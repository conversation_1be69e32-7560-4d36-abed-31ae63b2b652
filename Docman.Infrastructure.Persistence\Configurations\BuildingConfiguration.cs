namespace Docman.Infrastructure.Persistence.Configurations;


public class BuildingConfiguration : IEntityTypeConfiguration<Building>
{
    public void Configure(EntityTypeBuilder<Building> builder)
    {
        builder.HasKey(b => b.Id);

        builder.Property(b => b.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(b => b.Address)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(b => b.Description)
            .HasMaxLength(1000);

        builder.Property(b => b.Email)
            .HasMaxLength(100);

        builder.Property(b => b.MobileNumber)
            .HasMaxLength(20);

        builder.Property(b => b.DistrictOrVillageCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(b => b.Status)
            .HasConversion<int>() 
            .IsRequired();

        // Foreign key relationship
        builder.HasOne(b => b.DistrictOrVillage)
            .WithMany()
            .HasForeignKey(b => b.DistrictOrVillageCode)
            .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete
        
        builder.ToTable("Buildings");
    }
}
