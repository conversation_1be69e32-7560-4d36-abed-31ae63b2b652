namespace Docman.Core.Application.Common.DTOs.OrganizationalStructure;

/// <summary>
/// Request model for creating a new organizational node
/// </summary>
public class CreateOrganizationalNodeRequest
{
    /// <summary>
    /// Display name of the organizational node (required)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the organizational node
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Parent code for the new node (null for L1 nodes)
    /// The system will automatically generate the next available code
    /// </summary>
    public string? ParentCode { get; set; }
}