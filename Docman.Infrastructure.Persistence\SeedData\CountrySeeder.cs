﻿using System.Text.Json;
using Docman.Infrastructure.Persistence.Context;
using Microsoft.Extensions.Logging;

namespace Docman.Infrastructure.Persistence.SeedData;

public static class CountrySeeder
{
    public static async Task SeedAsync(DocmanDbContext context, ILogger? logger = null)
    {
        try
        {
            if (context.Countries.Any())
            {
                logger?.LogInformation("Countries already seeded, skipping...");
                return;
            }

            logger?.LogInformation("Starting countries data seeding...");

            var jsonPath = Path.Combine(
                AppContext.BaseDirectory,
                "SeedData",
                "egypt_admin_hierarchy.json"
            );

            string jsonData;
            if (File.Exists(jsonPath))
            {
                logger?.LogInformation("Reading seed data from: {JsonPath}", jsonPath);
                jsonData = await File.ReadAllTextAsync(jsonPath);
            }
            else
            {
                logger?.LogWarning("Seed file not found at path: {JsonPath}. Attempting to read embedded resource.", jsonPath);

                var assembly = typeof(CountrySeeder).Assembly;
                var resourceName = assembly
                    .GetManifestResourceNames()
                    .FirstOrDefault(n => n.EndsWith(".SeedData.egypt_admin_hierarchy.json", StringComparison.OrdinalIgnoreCase));

                if (resourceName == null)
                {
                    throw new FileNotFoundException($"Seed file not found: {jsonPath} and embedded resource missing.");
                }

                await using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    throw new FileNotFoundException($"Embedded resource not found: {resourceName}");
                }

                using var reader = new StreamReader(stream);
                jsonData = await reader.ReadToEndAsync();
                logger?.LogInformation("Read seed data from embedded resource: {ResourceName}", resourceName);
            }
            var countryData = JsonSerializer.Deserialize<CountryDataDto>(jsonData, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (countryData == null)
            {
                logger?.LogError("Failed to deserialize country seed data");
                throw new InvalidOperationException("Failed to deserialize seed data.");
            }

            logger?.LogInformation("Successfully deserialized country data for: {CountryName}", countryData.Name_En);

            var country = new Country
            {
                Code = countryData.Code,
                NameAr = countryData.Name_Ar,
                NameEn = countryData.Name_En,
                Governorates = countryData.Governorates?.Select(g => new Governorate
                {
                    Code = g.Code,
                    NameAr = g.Name_Ar,
                    NameEn = g.Name_En,
                    CountryCode = countryData.Code,
                    CityOrDistricts = g.Cities?.Select(c => new CityOrDistrict
                    {
                        Code = c.Code,
                        NameAr = c.Name_Ar,
                        NameEn = c.Name_En,
                        GovernorateCode = g.Code,
                        DistrictOrVillages = c.Districts?.Select(d => new DistrictOrVillage
                        {
                            Code = d.Code,
                            NameAr = d.Name_Ar,
                            CityOrDistrictCode = c.Code
                        }).ToList() ?? []
                    }).ToList() ?? []
                }).ToList() ?? []
            };

            logger?.LogInformation("Creating country entity with {GovernorateCount} governorates", 
                country.Governorates.Count);

            context.Countries.Add(country);
            await context.SaveChangesAsync();

            var totalCities = country.Governorates.Sum(g => g.CityOrDistricts.Count);
            var totalDistricts = country.Governorates.SelectMany(g => g.CityOrDistricts)
                .Sum(c => c.DistrictOrVillages.Count);

            logger?.LogInformation("Successfully seeded country: {CountryName} with {GovernorateCount} governorates, {CityCount} cities, and {DistrictCount} districts", 
                country.NameEn, country.Governorates.Count, totalCities, totalDistricts);
        }
        catch (JsonException ex)
        {
            logger?.LogError(ex, "JSON deserialization error while seeding countries data");
            throw new InvalidOperationException("Failed to parse seed data JSON", ex);
        }
        catch (FileNotFoundException ex)
        {
            logger?.LogError(ex, "Seed file not found");
            throw;
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Unexpected error occurred while seeding countries data");
            throw;
        }
    }

    // Overload for backward compatibility
    public static async Task SeedAsync(DocmanDbContext context)
    {
        await SeedAsync(context, null);
    }

    // DTOs matching the actual JSON structure
    private class CountryDataDto
    {
        public string Name_En { get; set; } = string.Empty;
        public string Name_Ar { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public List<GovernorateDto>? Governorates { get; set; }
    }

    private class GovernorateDto
    {
        public string Code { get; set; } = string.Empty;
        public string Name_Ar { get; set; } = string.Empty;
        public string Name_En { get; set; } = string.Empty;
        public List<CityDto>? Cities { get; set; }
    }

    private class CityDto
    {
        public string Code { get; set; } = string.Empty;
        public string Name_Ar { get; set; } = string.Empty;
        public string Name_En { get; set; } = string.Empty;
        public List<DistrictDto>? Districts { get; set; }
    }

    private class DistrictDto
    {
        public string Code { get; set; } = string.Empty;
        public string Name_Ar { get; set; } = string.Empty;
    }
}