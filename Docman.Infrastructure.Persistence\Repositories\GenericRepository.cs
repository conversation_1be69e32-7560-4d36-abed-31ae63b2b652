using Docman.Infrastructure.Persistence.Context;
using System.Linq.Expressions;
using Docman.Core.Application.Contracts;
using Docman.Core.Domain.Common;
using Microsoft.EntityFrameworkCore.Storage;

namespace Docman.Infrastructure.Persistence.Repositories;

/// <summary>
/// Generic repository implementation for all entity types
/// </summary>
/// <typeparam name="T">Entity type</typeparam>
public class GenericRepository<T> : IGenericRepository<T> where T : class
{
    private bool _disposed;
    protected readonly DocmanDbContext _context;
    private readonly DbSet<T> _dbSet;
    
    /// <summary>
    /// Protected access to the DbSet for derived repositories
    /// </summary>
    protected DbSet<T> DbSet => _dbSet;

    public GenericRepository(DocmanDbContext context)
    {
        _context = context;
        _dbSet = _context.Set<T>();
    }

    #region Query Methods

    public async Task<List<T>> GetAllAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default)
    {
        var query = tracked ? _dbSet : _dbSet.AsNoTracking();

        if (filter != null)
            query = query.Where(filter);

        if (includeProperties == null || includeProperties.Length == 0)
            return await query.ToListAsync(cancellationToken);

        foreach (var property in includeProperties)
            query = query.Include(property.Trim());

        return await query.ToListAsync(cancellationToken);
    }

    public async Task<T?> GetAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false,
        CancellationToken cancellationToken = default)
    {
        var query = tracked ? _dbSet : _dbSet.AsNoTracking();

        if (filter != null)
            query = query.Where(filter);

        if (includeProperties == null || includeProperties.Length == 0)
            return await query.FirstOrDefaultAsync(cancellationToken);

        foreach (var property in includeProperties)
            query = query.Include(property.Trim());

        return await query.FirstOrDefaultAsync(cancellationToken);
    }

    public Task<IQueryable<T>> GetForPaginationAsync(
        Expression<Func<T, bool>>? filter = null,
        string[]? includeProperties = null,
        bool tracked = false)
    {
        var query = tracked ? _dbSet : _dbSet.AsNoTracking();

        if (filter != null)
            query = query.Where(filter);

        if (includeProperties == null || includeProperties.Length == 0)
            return Task.FromResult(query);

        foreach (var property in includeProperties)
            query = query.Include(property.Trim());

        return Task.FromResult(query);
    }

    public async Task<bool> AnyAsync(
        Expression<Func<T, bool>> filter, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(filter, cancellationToken);
    }

    #endregion

    #region Command Methods

    public async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity), "Entity cannot be null.");

        try
        {
            // Audit properties are now handled automatically in DbContext
            await _dbSet.AddAsync(entity, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex)
        {
            throw new InvalidOperationException("An error occurred while saving the entity to the database.", ex);
        }

        return entity;
    }

    public async Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities), "Entities collection cannot be null.");

        try
        {
            var entitiesList = entities.ToList();
            // Audit properties are now handled automatically in DbContext
            await _dbSet.AddRangeAsync(entitiesList, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex)
        {
            throw new InvalidOperationException("An error occurred while saving the entities to the database.", ex);
        }
    }

    public async Task UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity), "Entity cannot be null.");

        try
        {
            // Audit properties are now handled automatically in DbContext
            _dbSet.Update(entity);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex)
        {
            throw new InvalidOperationException("An error occurred while updating the entity in the database.", ex);
        }
    }

    public async Task DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity), "Entity cannot be null.");

        try
        {
            // Use soft delete if entity inherits from AuditableEntity, otherwise hard delete
            if (entity is AuditableEntity)
            {
                // Soft delete is handled automatically in DbContext
                _dbSet.Remove(entity);
            }
            else
            {
                _dbSet.Remove(entity);
            }

            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex)
        {
            throw new InvalidOperationException("An error occurred while deleting the entity from the database.", ex);
        }
    }

    public async Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities), "Entities collection cannot be null.");

        try
        {
            var entitiesList = entities.ToList();
            // Soft delete is handled automatically in DbContext for AuditableEntity
            _dbSet.RemoveRange(entitiesList);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex)
        {
            throw new InvalidOperationException("An error occurred while deleting the entities from the database.", ex);
        }
    }

    #endregion

    #region Transaction Methods

    public Task<IDbContextTransaction> BeginTransactionAsync()
    {
        return _context.Database.BeginTransactionAsync();
    }

    public Task CommitTransactionAsync()
    {
        return _context.Database.CommitTransactionAsync();
    }

    public Task RollbackTransactionAsync()
    {
        return _context.Database.RollbackTransactionAsync();
    }

    #endregion

    #region IDisposable Implementation

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        if (_disposed)
            return;

        if (disposing)
            _context.Dispose();

        _disposed = true;
    }

    #endregion
}
