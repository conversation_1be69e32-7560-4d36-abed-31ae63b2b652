namespace Docman.Core.Application.Features.DocumentTypes.Commands.CreateDocumentType;

/// <summary>
/// Handler for CreateDocumentTypeCommand
/// </summary>
public sealed class CreateDocumentTypeCommandHandler(
    IGenericRepository<DocumentType> documentTypeRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreateDocumentTypeCommand, Result<int>>
{
    public async Task<Result<int>> Handle(CreateDocumentTypeCommand request, CancellationToken cancellationToken)
    {
        // Check if document type with the same name already exists
        var existingDocumentType = await documentTypeRepository.GetAsync(
            filter: dt => dt.Name == request.Name,
            cancellationToken: cancellationToken);

        if (existingDocumentType != null)
        {
            return Result.Failure<int>(DocumentTypeErrors.DocumentTypeAlreadyExists);
        }

        var documentType = request.Adapt<DocumentType>();

        // Add to repository
        await documentTypeRepository.AddAsync(documentType, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(documentType.Id);
    }
}