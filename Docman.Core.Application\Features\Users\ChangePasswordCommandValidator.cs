﻿
using Docman.Core.Application.Common.Constants;

namespace Docman.Core.Application.Features.Users;

public sealed class ChangePasswordCommandValidator : AbstractValidator<ChangePasswordCommand>
{
    public ChangePasswordCommandValidator()
    {
        RuleFor(x => x.UserId).NotEmpty();

        RuleFor(x => x.CurrentPassword).NotEmpty();

        RuleFor(x => x.NewPassword)
            .NotEmpty()
           .Matches(RegexPattern.Password)
           .WithMessage("Password should be at least 8 digits and should contains Lowercase, NonAlphanumeric and Uppercase")
           .NotEqual(x => x.CurrentPassword)
           .WithMessage("New Password cannot be the same as current password");
    }
}
