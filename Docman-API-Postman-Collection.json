{"info": {"_postman_id": "docman-api-collection", "name": "Docman API Collection", "description": "Complete and detailed API collection for the Docman Document Management System. Includes all endpoints with variables, example requests/responses, and tests to capture tokens and IDs.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "docman-system"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "Authentication", "description": "Authentication endpoints for login, token refresh, and logout operations.", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{auth_email}}\",\n  \"password\": \"{{auth_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}, "description": "Authenticate a user and retrieve an access token and refresh token. On success, this request stores tokens into collection variables for reuse."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });", "const json = pm.response.json();", "pm.expect(json).to.have.property('token');", "pm.expect(json).to.have.property('refreshToken');", "pm.collectionVariables.set('access_token', json.token);", "pm.collectionVariables.set('refresh_token', json.refreshToken);", "pm.collectionVariables.set('user_id', json.id || '');", "pm.test('Access token saved', function () { pm.expect(pm.collectionVariables.get('access_token')).to.be.a('string').and.not.empty; });"]}}], "response": [{"name": "200 OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin@123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"id\": \"7e1c3d0a-12ab-4b41-9aa7-2f3d1e2a9f00\",\n  \"email\": \"<EMAIL>\",\n  \"firstName\": \"System\",\n  \"lastName\": \"Admin\",\n  \"token\": \"eyJhbGciOi...\",\n  \"expiresIn\": 3600,\n  \"refreshToken\": \"f7a2a3c2-2c0a-4a23-9b10-44ec7c5d1f1b\",\n  \"refreshTokenExpiration\": \"2025-12-31T23:59:59Z\",\n  \"mustChangePassword\": false\n}"}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"{{access_token}}\",\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh-token"]}, "description": "Refresh access token using a valid refresh token. Updates the stored access token."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });", "const json = pm.response.json();", "pm.expect(json).to.have.property('token');", "pm.collectionVariables.set('access_token', json.token);"]}}], "response": []}, {"name": "Revoke Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"{{access_token}}\",\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/revoke-token", "host": ["{{base_url}}"], "path": ["api", "auth", "revoke-token"]}, "description": "Revoke the refresh token to log out the user."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });", "pm.collectionVariables.unset('access_token');", "pm.collectionVariables.unset('refresh_token');"]}}], "response": []}]}, {"name": "User Management", "description": "User management endpoints (requires permissions for certain actions).", "item": [{"name": "Create User", "request": {"auth": {"type": "bearer"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"{{new_user_first_name}}\",\n  \"lastName\": \"{{new_user_last_name}}\",\n  \"email\": \"{{new_user_email}}\",\n  \"password\": \"{{new_user_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/users/create-user", "host": ["{{base_url}}"], "path": ["api", "users", "create-user"]}, "description": "Create a new user account (requires AddUser permission)."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });", "const json = pm.response.json();", "pm.collectionVariables.set('created_user_id', json.userId || json.id || '');"]}}], "response": [{"name": "200 OK", "originalRequest": {}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"userId\": \"6d8b5c70-5f4f-4c2e-8c2a-10c442b6e001\"\n}"}]}, {"name": "Change Password", "request": {"auth": {"type": "bearer"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"{{current_password}}\",\n  \"newPassword\": \"{{new_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/users/change-password", "host": ["{{base_url}}"], "path": ["api", "users", "change-password"]}, "description": "Change the currently authenticated user's password."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });"]}}], "response": []}]}, {"name": "Buildings", "description": "Building management endpoints for CRUD operations on building records.", "item": [{"name": "Get All Buildings", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/buildings", "host": ["{{base_url}}"], "path": ["api", "buildings"]}, "description": "Get list of all buildings with basic information."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"Main Office Building\",\n    \"villageNameAr\": \"قرية\",\n    \"cityOrDistrictNameAr\": \"مدينة\",\n    \"cityOrDistrictNameEn\": \"City\",\n    \"governorateNameAr\": \"محافظة\",\n    \"governorateNameEn\": \"Governorate\",\n    \"countryNameAr\": \"مصر\",\n    \"countryNameEn\": \"Egypt\",\n    \"status\": 1\n  }\n]"}]}, {"name": "Get Building By ID", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/buildings/{{building_id}}", "host": ["{{base_url}}"], "path": ["api", "buildings", "{{building_id}}"]}, "description": "Get detailed information about a specific building."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "{\n  \"id\": 1,\n  \"name\": \"Main Office Building\",\n  \"address\": \"123 Business Street\",\n  \"description\": \"Primary office building for operations\",\n  \"email\": \"<EMAIL>\",\n  \"mobileNumber\": \"+1234567890\",\n  \"status\": 1,\n  \"districtOrVillageCode\": \"EG001001001\",\n  \"villageNameAr\": \"قرية\",\n  \"cityOrDistrictNameAr\": \"مدينة\",\n  \"cityOrDistrictNameEn\": \"City\",\n  \"governorateNameAr\": \"محافظة\",\n  \"governorateNameEn\": \"Governorate\",\n  \"countryNameAr\": \"مصر\",\n  \"countryNameEn\": \"Egypt\",\n  \"createdByID\": \"7e1c3d0a-12ab-4b41-9aa7-2f3d1e2a9f00\",\n  \"createdAt\": \"2025-08-14T10:00:00Z\",\n  \"updatedByID\": null,\n  \"updatedAt\": null\n}"}]}, {"name": "Create Building", "request": {"auth": {"type": "bearer"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{building_name}}\",\n  \"address\": \"{{building_address}}\",\n  \"description\": \"{{building_description}}\",\n  \"email\": \"{{building_email}}\",\n  \"mobileNumber\": \"{{building_mobile}}\",\n  \"districtOrVillageCode\": \"{{district_or_village_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/buildings", "host": ["{{base_url}}"], "path": ["api", "buildings"]}, "description": "Create a new building with default status as Unapproved."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () { pm.response.to.have.status(201); });", "const json = pm.response.json();", "pm.collectionVariables.set('building_id', json.id || json.Id || pm.response.headers.get('Location')?.split('/').pop() || '');"]}}], "response": [{"name": "201 Created", "status": "Created", "code": 201, "_postman_previewlanguage": "json", "body": "{\n  \"id\": 2\n}"}]}, {"name": "Update Building", "request": {"auth": {"type": "bearer"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{building_name}} (Updated)\",\n  \"address\": \"{{building_address}}\",\n  \"description\": \"Updated description\",\n  \"email\": \"{{building_email}}\",\n  \"mobileNumber\": \"{{building_mobile}}\",\n  \"districtOrVillageCode\": \"{{district_or_village_code}}\",\n  \"status\": {{building_status}}\n}"}, "url": {"raw": "{{base_url}}/api/buildings/{{building_id}}", "host": ["{{base_url}}"], "path": ["api", "buildings", "{{building_id}}"]}, "description": "Update an existing building. Status: 0=Unapproved, 1=Approved, 2=Frozen."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });"]}}], "response": []}]}, {"name": "Locations", "description": "Location hierarchy endpoints for countries, governorates, cities, and districts.", "item": [{"name": "Get All Countries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/location/countries", "host": ["{{base_url}}"], "path": ["api", "location", "countries"]}, "description": "Get list of all countries with localized names."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"code\": \"EG\",\n    \"nameAr\": \"مصر\",\n    \"nameEn\": \"Egypt\"\n  }\n]"}]}, {"name": "Get Governorates By Country", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/location/countries/{{country_code}}/governorates", "host": ["{{base_url}}"], "path": ["api", "location", "countries", "{{country_code}}", "governorates"]}, "description": "Get governorates for a specific country (e.g., EG for Egypt)."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"code\": \"EG001\",\n    \"nameAr\": \"القاهرة\",\n    \"nameEn\": \"Cairo\"\n  }\n]"}]}, {"name": "Get Cities By Governorate", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/location/governorates/{{governorate_code}}/cities", "host": ["{{base_url}}"], "path": ["api", "location", "governorates", "{{governorate_code}}", "cities"]}, "description": "Get cities for a specific governorate."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"code\": \"EG001001\",\n    \"nameAr\": \"مدينة\",\n    \"nameEn\": \"City\"\n  }\n]"}]}, {"name": "Get Districts By City", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/location/cities/{{city_code}}/districts", "host": ["{{base_url}}"], "path": ["api", "location", "cities", "{{city_code}}", "districts"]}, "description": "Get districts/villages for a specific city."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"code\": \"EG001001001\",\n    \"nameAr\": \"قرية\"\n  }\n]"}]}]}, {"name": "Organizational Structure", "description": "Organizational structure management with hierarchical Base-36 coded nodes.", "item": [{"name": "Get Organizational Tree", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/organizationalstructure/tree", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure", "tree"]}, "description": "Get complete organizational tree structure."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "{\n  \"rootNodes\": [\n    { \n      \"code\": \"11000\", \n      \"name\": \"Central Administration\", \n      \"description\": null, \n      \"level\": 1, \n      \"children\": [] \n    }\n  ],\n  \"levelCounts\": { \n    \"1\": 1, \n    \"2\": 0, \n    \"3\": 0, \n    \"4\": 0 \n  },\n  \"totalNodes\": 1\n}"}]}, {"name": "Get Node By Code", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/organizationalstructure/{{node_code}}?includeChildren={{include_children}}", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure", "{{node_code}}"], "query": [{"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{{include_children}}"}]}, "description": "Get organizational node details by 5-character Base-36 code."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "{\n  \"code\": \"ABCD1\",\n  \"name\": \"General Administration\",\n  \"description\": null,\n  \"level\": 2,\n  \"parentCode\": \"11000\",\n  \"parent\": { \n    \"code\": \"11000\", \n    \"name\": \"Central Administration\", \n    \"description\": null, \n    \"level\": 1, \n    \"parentCode\": null \n  },\n  \"children\": [],\n  \"pathToRoot\": [\n    \"11000\", \n    \"ABCD1\"\n  ],\n  \"childrenCount\": 0,\n  \"descendantsCount\": 0\n}"}]}, {"name": "Get Nodes By Level", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/organizationalstructure/levels/{{organizational_level}}", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure", "levels", "{{organizational_level}}"]}, "description": "Get all nodes at a specific level (L1, L2, L3, L4)."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"code\": \"11000\",\n    \"name\": \"Central Administration\",\n    \"description\": null,\n    \"level\": 1,\n    \"parentCode\": null\n  }\n]"}]}, {"name": "Get Path To Root", "request": {"auth": {"type": "bearer"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/organizationalstructure/{{node_code}}/path-to-root", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure", "{{node_code}}", "path-to-root"]}, "description": "Get complete path from node to root."}, "response": [{"name": "200 OK", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": "[\n  {\n    \"code\": \"11000\",\n    \"name\": \"Central Administration\"\n  },\n  {\n    \"code\": \"ABCD1\",\n    \"name\": \"General Administration\"\n  }\n]"}]}, {"name": "Create Organizational Node", "request": {"auth": {"type": "bearer"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{org_node_name}}\",\n  \"description\": \"{{org_node_description}}\",\n  \"parentCode\": \"{{org_parent_code}}\"\n}"}, "url": {"raw": "{{base_url}}/api/organizationalstructure", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure"]}, "description": "Create new organizational node with automatic code generation. Leave parentCode empty for L1 nodes."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () { pm.response.to.have.status(201); });", "const json = pm.response.json();", "pm.collectionVariables.set('node_code', json.code || '');"]}}], "response": [{"name": "201 Created", "status": "Created", "code": 201, "_postman_previewlanguage": "json", "body": "{\n  \"code\": \"ABCDE\",\n  \"name\": \"New Department\",\n  \"description\": null,\n  \"level\": 2,\n  \"parentCode\": \"11000\"\n}"}]}, {"name": "Update Organizational Node", "request": {"auth": {"type": "bearer"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"{{org_node_name}} (Updated)\",\n  \"description\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/api/organizationalstructure/{{node_code}}", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure", "{{node_code}}"]}, "description": "Update existing organizational node. Note: Code cannot be changed."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () { pm.response.to.have.status(200); });"]}}], "response": []}, {"name": "Delete Organizational Node", "request": {"auth": {"type": "bearer"}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/organizationalstructure/{{node_code}}?cascadeDelete={{cascade_delete}}", "host": ["{{base_url}}"], "path": ["api", "organizationalstructure", "{{node_code}}"], "query": [{"key": "cascadeDelete", "value": "{{cascade_delete}}"}]}, "description": "Delete organizational node with optional cascade deletion."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 204', function () { pm.response.to.have.status(204); });"]}}], "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Automatically choose HTTPS or HTTP base URL", "const useHttps = pm.collectionVariables.get('use_https');", "pm.collectionVariables.set('base_url', useHttps === 'true' ? pm.collectionVariables.get('base_url_https') : pm.collectionVariables.get('base_url_http'));"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response time assertion", "pm.test('Response time < 2s', function () { pm.expect(pm.response.responseTime).to.be.below(2000); });"]}}], "variable": [{"key": "use_https", "value": "true", "description": "If true, uses HTTPS base URL; otherwise HTTP."}, {"key": "base_url_https", "value": "https://localhost:7000", "description": "Base URL for the Docman API (HTTPS)"}, {"key": "base_url_http", "value": "http://localhost:6000", "description": "Base URL for the Docman API (HTTP)"}, {"key": "base_url", "value": "https://localhost:7000", "description": "Computed base URL used by requests."}, {"key": "auth_email", "value": "<EMAIL>", "description": "Login email."}, {"key": "auth_password", "value": "Admin@123", "description": "Login password."}, {"key": "access_token", "value": "", "description": "JWT access token obtained from login."}, {"key": "refresh_token", "value": "", "description": "Refresh token obtained from login."}, {"key": "user_id", "value": "", "description": "Authenticated user Id from login response."}, {"key": "new_user_first_name", "value": "<PERSON>", "description": "First name for user creation."}, {"key": "new_user_last_name", "value": "<PERSON><PERSON>", "description": "Last name for user creation."}, {"key": "new_user_email", "value": "<EMAIL>", "description": "Email for user creation."}, {"key": "new_user_password", "value": "SecurePassword123!", "description": "Password for user creation."}, {"key": "created_user_id", "value": "", "description": "Captured user Id from create user response."}, {"key": "current_password", "value": "Admin@123", "description": "Current password for change-password."}, {"key": "new_password", "value": "Admin@124", "description": "New password for change-password."}, {"key": "building_id", "value": "1", "description": "Building Id used in building requests."}, {"key": "building_name", "value": "Main Office Building", "description": "Building name."}, {"key": "building_address", "value": "123 Business Street", "description": "Building address."}, {"key": "building_description", "value": "Primary office building for operations", "description": "Building description."}, {"key": "building_email", "value": "<EMAIL>", "description": "Building email."}, {"key": "building_mobile", "value": "+1234567890", "description": "Building mobile."}, {"key": "district_or_village_code", "value": "EG001001001", "description": "District/Village code for the building location."}, {"key": "building_status", "value": "1", "description": "Building status (0=Unapproved,1=Approved,2=Frozen)."}, {"key": "country_code", "value": "EG", "description": "Country code (e.g., EG)."}, {"key": "governorate_code", "value": "EG001", "description": "Governorate code."}, {"key": "city_code", "value": "EG001001", "description": "City code."}, {"key": "node_code", "value": "ABCD1", "description": "Organizational node code (5-char Base-36)."}, {"key": "org_node_name", "value": "New Department", "description": "Name for organizational node create/update."}, {"key": "org_node_description", "value": "Description of the new department", "description": "Description for organizational node."}, {"key": "org_parent_code", "value": "", "description": "Parent code (leave empty for L1 nodes, or provide a 5-char code for child nodes)."}, {"key": "organizational_level", "value": "L1", "description": "Organizational level (L1, L2, L3, L4)."}, {"key": "include_children", "value": "false", "description": "Whether to include children when getting a node by code."}, {"key": "cascade_delete", "value": "false", "description": "Whether to cascade delete descendants when deleting a node."}]}