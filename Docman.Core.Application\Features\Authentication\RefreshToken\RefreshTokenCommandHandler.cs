﻿

using Docman.Core.Application.Common.DTOs;
using Docman.Core.Application.Contracts;
using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.Authentication.RefreshToken;

public sealed class RefreshTokenCommandHandler(IAuthRepository authRepository) 
    : IRequestHandler<RefreshTokenCommand,Result<AuthResponse>>
{
    private readonly IAuthRepository _authRepository = authRepository;

    public async Task<Result<AuthResponse>> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        return await _authRepository.GetRefreshTokenAsync(request.Token,request.RefreshToken,cancellationToken);
    }
}
