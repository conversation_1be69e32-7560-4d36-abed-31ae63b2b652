﻿
using Docman.Core.Application.Common.Constants;

namespace Docman.Infrastructure.Persistence.Configurations;
 public class RoleConfiguration : IEntityTypeConfiguration<ApplicationRole>
 {
    public void Configure(EntityTypeBuilder<ApplicationRole> builder)
    {


        builder.HasData([
            new ApplicationRole
                {
                    Id = RoleSeedData.AdminRoleId,
                    Name = DefaultRoleNames.Admin,
                    NormalizedName = DefaultRoleNames.Admin.ToUpper(),
                    ConcurrencyStamp = RoleSeedData.AdminRoleConcurrencyStamp,
                },
            new ApplicationRole
            {
                Id = RoleSeedData.EmployeeRoleId,
                Name= DefaultRoleNames.Employee,
                NormalizedName= DefaultRoleNames.Employee.ToUpper(),
                ConcurrencyStamp= RoleSeedData.EmployeeRoleConcurrencyStamp,
                IsDefault = true
            }

        ]);

    }
 }
