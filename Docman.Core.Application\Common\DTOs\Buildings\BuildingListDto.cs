using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Common.DTOs.Buildings;

/// <summary>
/// DTO for building list view containing essential information for listing
/// </summary>
public sealed record BuildingListDto
{
    public int Id { get; init; }
    
    public string Name { get; init; } = string.Empty;
    
    public string VillageNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// Arabic name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the country derived from governorate location
    /// </summary>
    public string CountryNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the country derived from governorate location
    /// </summary>
    public string CountryNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Current status of the building
    /// </summary>
    public BuildingStatus Status { get; init; }
}
