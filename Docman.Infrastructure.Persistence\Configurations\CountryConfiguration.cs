﻿namespace Docman.Infrastructure.Persistence.Configurations;

public class CountryConfiguration : IEntityTypeConfiguration<Country>
{
    public void Configure(EntityTypeBuilder<Country> builder)
    {
        builder.HasKey(c => c.Code);

        builder.Property(c => c.Code)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(c => c.NameAr)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(c => c.NameEn)
            .HasMaxLength(200)
            .IsRequired();

        builder.HasMany(c => c.Governorates)
            .WithOne(g => g.Country)
            .HasForeignKey(g => g.CountryCode)
            .OnDelete(DeleteBehavior.Cascade);
    }
}