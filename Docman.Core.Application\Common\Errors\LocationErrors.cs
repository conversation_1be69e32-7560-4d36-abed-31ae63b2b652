namespace Docman.Core.Application.Common.Errors;

public static class LocationErrors
{
    public static readonly Error CountryNotFound = new("Location.CountryNotFound", "The specified country was not found.");
    
    public static readonly Error GovernorateNotFound = new("Location.GovernorateNotFound", "The specified governorate was not found.");
    
    public static readonly Error CityNotFound = new("Location.CityNotFound", "The specified city was not found.");
    
    public static readonly Error DistrictNotFound = new("Location.DistrictNotFound", "The specified district was not found.");
    
    public static readonly Error InvalidCountryCode = new("Location.InvalidCountryCode", "The provided country code is invalid.");
    
    public static readonly Error InvalidGovernorateCode = new("Location.InvalidGovernorateCode", "The provided governorate code is invalid.");
    
    public static readonly Error InvalidCityCode = new("Location.InvalidCityCode", "The provided city code is invalid.");
}
