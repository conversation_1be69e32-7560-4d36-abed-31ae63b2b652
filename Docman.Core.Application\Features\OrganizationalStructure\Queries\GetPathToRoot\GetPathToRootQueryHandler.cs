using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Queries.GetPathToRoot;

/// <summary>
/// Handler for retrieving the path from a node to the root
/// </summary>
public sealed class GetPathToRootQueryHandler(
    IOrganizationalNodeRepository organizationalNodeRepository
) : IRequestHandler<GetPathToRootQuery, Result<List<OrganizationalNodeDto>>>
{
    public async Task<Result<List<OrganizationalNodeDto>>> Handle(
        GetPathToRootQuery request,
        CancellationToken cancellationToken)
    {
        // First verify the node exists
        var node = await organizationalNodeRepository.GetByCodeAsync(
            request.Code,
            cancellationToken: cancellationToken);

        if (node is null)
            return Result.Failure<List<OrganizationalNodeDto>>(OrganizationalNodeErrors.NodeNotFound);

        // Get the complete path
        var pathNodes = await organizationalNodeRepository.GetPathToRootAsync(
            request.Code,
            cancellationToken);

        var result = pathNodes.Adapt<List<OrganizationalNodeDto>>();

        return Result.Success(result);
    }
}
