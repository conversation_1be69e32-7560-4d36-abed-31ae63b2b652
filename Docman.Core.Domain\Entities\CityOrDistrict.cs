﻿namespace Docman.Core.Domain.Entities;

public class CityOrDistrict
{
    public string Code { get; set; }
    public string NameAr { get; set; }
    public string NameEn { get; set; }
    public string GovernorateCode { get; set; }

    // Navigation property to Governorate
    public Governorate Governorate { get; set; }
    public ICollection<DistrictOrVillage> DistrictOrVillages { get; set; } = new List<DistrictOrVillage>();
}