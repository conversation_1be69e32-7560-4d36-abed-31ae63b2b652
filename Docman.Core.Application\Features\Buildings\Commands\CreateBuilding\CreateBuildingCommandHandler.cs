using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Buildings.Commands.CreateBuilding;

/// <summary>
/// Handler for CreateBuildingCommand
/// </summary>
public sealed class CreateBuildingCommandHandler(
    IGenericRepository<Building> buildingRepository,
    IGenericRepository<DistrictOrVillage> districtOrVillageRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreateBuildingCommand, Result<int>>
{
    public async Task<Result<int>> Handle(CreateBuildingCommand request, CancellationToken cancellationToken)
    {
        // Verify that the district or village exists
        var districtOrVillage = await districtOrVillageRepository.GetAsync(
            filter: d => d.Code == request.DistrictOrVillageCode,
            cancellationToken: cancellationToken);


        if (districtOrVillage == null)
        {
            return Result.Failure<int>(BuildingErrors.DistrictOrVillageNotFound);
        }

        var building = request.Adapt<Building>();

        // Add to repository
        await buildingRepository.AddAsync(building, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(building.Id);
    }
}