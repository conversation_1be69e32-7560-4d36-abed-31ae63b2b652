using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Common.DTOs.Stocks;

/// <summary>
/// DTO for detailed stock information including all properties, location details, and audit data
/// </summary>
public sealed record StockDetailsDto
{
    /// <summary>
    /// Unique identifier of the stock
    /// </summary>
    public int Id { get; init; }

    /// <summary>
    /// Name of the stock
    /// </summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>
    /// Name of the person responsible for the stock
    /// </summary>
    public string ManagerName { get; init; } = string.Empty;

    /// <summary>
    /// Code of the village where the stock is located
    /// </summary>
    public string VillageCode { get; init; } = string.Empty;

    /// <summary>
    /// Detailed address of the stock
    /// </summary>
    public string Address { get; init; } = string.Empty;

    /// <summary>
    /// Phone number for maintenance
    /// </summary>
    public string? MaintenancePhone { get; init; }

    /// <summary>
    /// Phone number for security
    /// </summary>
    public string? SecurityPhone { get; init; }

    /// <summary>
    /// Email address for the stock
    /// </summary>
    public string? Email { get; init; }

    /// <summary>
    /// Additional details about the stock
    /// </summary>
    public string? Description { get; init; }

    /// <summary>
    /// Current status of the stock
    /// </summary>
    public StockStatus Status { get; init; }

    // Location details
    /// <summary>
    /// Arabic name of the village where the stock is located
    /// </summary>
    public string VillageNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// Arabic name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the city derived from village location
    /// </summary>
    public string CityOrDistrictNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the governorate derived from village location
    /// </summary>
    public string GovernorateNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Arabic name of the country derived from governorate location
    /// </summary>
    public string CountryNameAr { get; init; } = string.Empty;
    
    /// <summary>
    /// English name of the country derived from governorate location
    /// </summary>
    public string CountryNameEn { get; init; } = string.Empty;

    /// <summary>
    /// Associated phone numbers
    /// </summary>
    public ICollection<StockPhoneDto> StockPhones { get; init; } = new List<StockPhoneDto>();

    // Audit properties
    /// <summary>
    /// User ID who created this stock
    /// </summary>
    public string? CreatedByID { get; init; }

    /// <summary>
    /// Timestamp when this stock was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; init; }

    /// <summary>
    /// User ID who last updated this stock
    /// </summary>
    public string? UpdatedByID { get; init; }

    /// <summary>
    /// Timestamp when this stock was last updated
    /// </summary>
    public DateTimeOffset? UpdatedAt { get; init; }
}
