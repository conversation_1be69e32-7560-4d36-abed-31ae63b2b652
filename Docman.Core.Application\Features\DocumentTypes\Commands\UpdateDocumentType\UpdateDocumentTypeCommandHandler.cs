namespace Docman.Core.Application.Features.DocumentTypes.Commands.UpdateDocumentType;

/// <summary>
/// Handler for UpdateDocumentTypeCommand
/// </summary>
public sealed class UpdateDocumentTypeCommandHandler(
    IGenericRepository<DocumentType> documentTypeRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<UpdateDocumentTypeCommand, Result>
{
    public async Task<Result> Handle(UpdateDocumentTypeCommand request, CancellationToken cancellationToken)
    {
        // Find the existing document type by ID
        var documentType = await documentTypeRepository.GetAsync(
            filter: dt => dt.Id == request.Id,
            cancellationToken: cancellationToken);

        if (documentType == null)
        {
            return Result.Failure(DocumentTypeErrors.DocumentTypeNotFound);
        }

        // If name is being changed, check if new name already exists (excluding current record)
        if (documentType.Name != request.Name)
        {
            var existingWithNewName = await documentTypeRepository.GetAsync(
                filter: dt => dt.Name == request.Name && dt.Id != request.Id,
                cancellationToken: cancellationToken);

            if (existingWithNewName != null)
            {
                return Result.Failure(DocumentTypeErrors.DocumentTypeAlreadyExists);
            }
        }

        // Update properties
        documentType.Name = request.Name;
        documentType.Status = request.Status;

        // Update in repository
        await documentTypeRepository.UpdateAsync(documentType, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

