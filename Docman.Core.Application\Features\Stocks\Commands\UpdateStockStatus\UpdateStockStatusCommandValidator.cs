namespace Docman.Core.Application.Features.Stocks.Commands.UpdateStockStatus;

/// <summary>
/// Validator for UpdateStockStatusCommand
/// </summary>
public sealed class UpdateStockStatusCommandValidator : AbstractValidator<UpdateStockStatusCommand>
{
    public UpdateStockStatusCommandValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Stock ID must be greater than 0");

        RuleFor(x => x.NewStatus)
            .IsInEnum()
            .WithMessage("Invalid stock status value");
    }
}
