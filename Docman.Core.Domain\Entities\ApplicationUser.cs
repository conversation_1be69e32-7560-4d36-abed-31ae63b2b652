﻿using Microsoft.AspNetCore.Identity;

namespace Docman.Core.Domain.Entities;

public class ApplicationUser:IdentityUser
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? ProfilePictureUrl { get; set; }
    public bool MustChangePassword { get; set; } = true;
    public List<RefreshToken> RefreshTokens { get; set; } = [];
}