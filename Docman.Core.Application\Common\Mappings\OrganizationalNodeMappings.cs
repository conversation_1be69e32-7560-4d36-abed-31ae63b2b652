using Mapster;

namespace Docman.Core.Application.Common.Mappings;

/// <summary>
/// Mapster configuration for organizational node mappings
/// </summary>
public class OrganizationalNodeMappings : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // OrganizationalNode to OrganizationalNodeDto
        config.NewConfig<OrganizationalNode, OrganizationalNodeDto>();

        // OrganizationalNode to OrganizationalNodeDetailsDto
        config.NewConfig<OrganizationalNode, OrganizationalNodeDetailsDto>()
            .Map(dest => dest.PathToRoot, src => OrganizationalCodeHelper.GetPathToRoot(src.Code))
            .Map(dest => dest.ChildrenCount, src => src.Children.Count())
            .Map(dest => dest.DescendantsCount, src => CountDescendants(src));

        // OrganizationalNode to OrganizationalTreeNodeDto
        config.NewConfig<OrganizationalNode, OrganizationalTreeNodeDto>()
            .Map(dest => dest.Children, src => src.Children.Adapt<List<OrganizationalTreeNodeDto>>());

        // CreateOrganizationalNodeRequest to OrganizationalNode
        config.NewConfig<CreateOrganizationalNodeRequest, OrganizationalNode>()
            .Map(dest => dest.Code, src => string.Empty) // Code will be generated
            .Map(dest => dest.Level, src => OrganizationalLevel.L1) // Will be calculated
            .Map(dest => dest.ParentCode, src => (string?)null) // Will be calculated
            .Ignore(dest => dest.Parent)
            .Ignore(dest => dest.Children);

        // UpdateOrganizationalNodeRequest mappings
        config.NewConfig<UpdateOrganizationalNodeRequest, OrganizationalNode>()
            .Ignore(dest => dest.Code) // Code cannot be changed
            .Ignore(dest => dest.Level) // Derived from code
            .Ignore(dest => dest.ParentCode) // Derived from code
            .Ignore(dest => dest.Parent)
            .Ignore(dest => dest.Children);
    }

    /// <summary>
    /// Recursively counts all descendants of a node
    /// </summary>
    private static int CountDescendants(OrganizationalNode node)
    {
        var count = 0;
        foreach (var child in node.Children)
        {
            count++; // Count the child
            count += CountDescendants(child); // Count the child's descendants
        }
        return count;
    }
}
