﻿
using Docman.Core.Application.Common.Constants;
using Docman.Core.Application.Common.DTOs.Users;
using Docman.Core.Application.Features.Users;
using Docman.Infrastructure.Persistence.Authentication.Filters;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace Docman.Presentation.API.Controllers
{
    /// <summary>
    /// Manage user accounts - create users and change passwords.
    /// </summary>
    /// <remarks>
    /// 🔐 **Authentication required** for all endpoints.
    /// 👥 **Perfect for:** User management, account settings, admin panels.
    /// 🔑 Some endpoints require special permissions.
    /// </remarks>
    [SwaggerResponse(200, "Operation completed successfully")]
    [SwaggerResponse(400, "Invalid request data")]
    [SwaggerResponse(401, "User is not authorized")]
    [SwaggerResponse(403, "Insufficient permissions")]
    public class UsersController(IMediator mediator) : BaseController
    {
        private readonly IMediator _mediator = mediator;

        /// <summary>
        /// Create a new user account in the system.
        /// </summary>
        /// <param name="request">User information (first name, last name, email, password)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>
        /// ID of the newly created user account.
        /// </returns>
        [HttpPost("create-user"), SwaggerOperation(
            Summary = "Create new user account",
            Description = "**Create a new user account with basic information.**<br/><br/>👤 **Required information:**<br/>• First name<br/>• Last name<br/>• Email address (must be unique)<br/>• Password<br/><br/>🔑 **Permission required:** 'AddUser' permission<br/>• Only authorized admins can create users<br/>• Not for self-registration<br/><br/>✅ **Returns:** New user ID for reference<br/><br/>🎯 **Use this to:** Admin user management, employee onboarding, account creation forms")]
        [HasPermission(Permissions.AddUser)]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request,CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new CreateUserCommand(request.FirstName, request.LastName, request.Email, request.Password),cancellationToken);
            return result.IsSuccess ? Ok(new { UserId = result.Value }) : BadRequest(result.Error);
        }

        /// <summary>
        /// Change current user's password.
        /// </summary>
        /// <param name="request">Current password and new password</param>
        /// <returns>
        /// Success confirmation that password was changed.
        /// </returns>
        [HttpPost("change-password"), SwaggerOperation(
            Summary = "Change user password",
            Description = "**Allow users to change their own password.**<br/><br/>🔒 **Required:**<br/>• Current password (for verification)<br/>• New password (must meet security requirements)<br/><br/>🔐 **Security features:**<br/>• Validates current password before change<br/>• Only users can change their own password<br/>• Automatic authentication required<br/><br/>✅ **Success:** Password updated immediately<br/><br/>🎯 **Use this to:** Account settings, security pages, password change forms")]
        [Authorize]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var result = await _mediator.Send(new ChangePasswordCommand(userId!, request.CurrentPassword, request.NewPassword));
            return result.IsSuccess ? Ok(new { Message = "Password changed successfully" }) : BadRequest(result.Error);
        }
    }
}
