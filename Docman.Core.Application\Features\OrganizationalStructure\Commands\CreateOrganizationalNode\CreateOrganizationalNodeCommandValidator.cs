namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.CreateOrganizationalNode;

/// <summary>
/// Validator for CreateOrganizationalNodeCommand
/// </summary>
public sealed class CreateOrganizationalNodeCommandValidator : AbstractValidator<CreateOrganizationalNodeCommand>
{
    public CreateOrganizationalNodeCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Name is required.")
            .MaximumLength(200)
            .WithMessage("Name cannot exceed 200 characters.")
            .Matches(@"^[\w\s\-\.]+$")
            .WithMessage("Name can only contain letters, numbers, spaces, hyphens, and periods.");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters.")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.ParentCode)
            .Must(BeValidCodeFormat)
            .WithMessage("Parent code must be exactly 5 characters using Base-36 format (0-9, A-Z).")
            .When(x => !string.IsNullOrEmpty(x.ParentCode));
    }

    /// <summary>
    /// Validates the Base-36 code format
    /// </summary>
    private static bool BeValidCodeFormat(string? code)
    {
        return string.IsNullOrEmpty(code) ||
               OrganizationalCodeHelper.IsValidCode(code);
    }
}