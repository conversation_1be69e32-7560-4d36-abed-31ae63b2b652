using Docman.Core.Application.Common.DTOs.DocumentTypes;
using Docman.Core.Application.Features.DocumentTypes.Commands.CreateDocumentType;
using Docman.Core.Application.Features.DocumentTypes.Commands.UpdateDocumentType;
using Docman.Core.Application.Features.DocumentTypes.Commands.ChangeDocumentTypeStatus;
using Docman.Core.Application.Features.DocumentTypes.Queries.GetAllDocumentTypes;
using Docman.Core.Application.Features.DocumentTypes.Queries.GetDocumentTypeByName;
using Docman.Core.Application.Features.DocumentTypes.Queries.GetDocumentTypeById;
using Mapster;

namespace Docman.Presentation.API.Controllers;

/// <summary>
/// Manage document type records - view, create, and update document type information.
/// </summary>
/// <remarks>
/// 🔐 All endpoints require authentication (Bearer token).
/// 📄 **Perfect for:** Document classification systems, document management workflows.
/// 📋 Each document type has a unique name and approval status for workflow control.
/// </remarks>
[SwaggerResponse(200, "Operation completed successfully")]
[SwaggerResponse(400, "Invalid request data")]
[SwaggerResponse(401, "User is not authorized")]
[SwaggerResponse(404, "Document type not found")]
[Authorize] // Require authorization for all endpoints
public class DocumentTypesController(IMediator mediator) : BaseController
{
    /// <summary>
    /// Get list of all document types with basic information.
    /// </summary>
    /// <returns>
    /// List of document types with name and status.
    /// </returns>
    [HttpGet, SwaggerOperation(
        Summary = "Get all document types",
        Description = "**Get complete list of all document types in the system.**<br/><br/>📋 **What you get:**<br/>• Document type names<br/>• Approval status (Approved, Unapproved, Frozen)<br/><br/>🎯 **Use this to:** Build document type lists, classification dropdowns, management interfaces")]
    [ProducesResponseType(typeof(IEnumerable<DocumentTypeListDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetAllDocumentTypes()
    {
        var query = new GetAllDocumentTypesQuery();
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Get detailed information about a specific document type by ID.
    /// </summary>
    /// <param name="id">The unique ID of the document type</param>
    /// <returns>
    /// Complete document type details including ID, name and status.
    /// </returns>
    [HttpGet("{id:int}"), SwaggerOperation(
        Summary = "Get document type details by ID",
        Description = "**Get complete information about a specific document type by its ID.**<br/><br/>📄 **Detailed info includes:**<br/>• Document type ID<br/>• Document type name<br/>• Current approval status<br/><br/>💡 **Example:** GET `/123` - Get details for document type with ID 123<br/><br/>🎯 **Use this to:** Document type detail pages, edit forms, status checks")]
    [ProducesResponseType(typeof(DocumentTypeDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDocumentTypeById(int id)
    {
        var query = new GetDocumentTypeByIdQuery(id);
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Get detailed information about a specific document type by name (for backward compatibility).
    /// </summary>
    /// <param name="name">The unique name of the document type</param>
    /// <returns>
    /// Complete document type details including ID, name and status.
    /// </returns>
    [HttpGet("by-name/{name}"), SwaggerOperation(
        Summary = "Get document type details by name",
        Description = "**Get complete information about a specific document type by its name.**<br/><br/>📄 **Detailed info includes:**<br/>• Document type ID<br/>• Document type name<br/>• Current approval status<br/><br/>💡 **Example:** GET `/by-name/Invoice` - Get details for document type named 'Invoice'<br/><br/>🎯 **Use this to:** Document type detail pages, edit forms, status checks<br/><br/>⚠️ **Note:** This endpoint is kept for backward compatibility. Consider using the ID-based endpoint instead.")]
    [ProducesResponseType(typeof(DocumentTypeDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDocumentTypeByName(string name)
    {
        var query = new GetDocumentTypeByNameQuery(name);
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Add a new document type to the system.
    /// </summary>
    /// <param name="request">Document type information (name)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// ID of the newly created document type.
    /// </returns>
    [HttpPost, SwaggerOperation(
        Summary = "Create new document type",
        Description = "**Add a new document type to the system.**<br/><br/>📝 **Required information:**<br/>• Unique document type name<br/><br/>✨ **What happens:**<br/>• Document type is created with 'Unapproved' status<br/>• Name must be unique across all document types<br/><br/>🎯 **Use this to:** Add new classification forms, document type registration")]
    [ProducesResponseType(typeof(int), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateDocumentType([FromBody] CreateDocumentTypeRequest request,
        CancellationToken cancellationToken)
    {
        var command = request.Adapt<CreateDocumentTypeCommand>();
        var result = await mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return CreatedAtAction(
                nameof(GetDocumentTypeById),
                new { id = result.Value },
                new { Id = result.Value }
            );
        }

        return result.ToActionResult();
    }

    /// <summary>
    /// Update existing document type information.
    /// </summary>
    /// <param name="id">The ID of the document type to update</param>
    /// <param name="request">Updated document type information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// Success confirmation that document type was updated.
    /// </returns>
    [HttpPut("{id:int}"), SwaggerOperation(
        Summary = "Update document type information",
        Description = "**Update an existing document type's information.**<br/><br/>✏️ **What you can update:**<br/>• Document type name (must remain unique)<br/>• Status (Approved, Unapproved, Frozen)<br/><br/>🎯 **Use this to:** Edit document type forms, rename types, update status")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateDocumentType(
        int id,
        [FromBody] UpdateDocumentTypeRequest request,
        CancellationToken cancellationToken)
    {
        // Create command with ID from route and new values from request
        var command = new UpdateDocumentTypeCommand(id, request.Name, request.Status);
        var result = await mediator.Send(command, cancellationToken);

        return result.ToActionResult();
    }

    /// <summary>
    /// Change the status of a document type.
    /// </summary>
    /// <param name="id">The ID of the document type</param>
    /// <param name="request">Status change information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// Success confirmation that status was changed.
    /// </returns>
    [HttpPatch("{id:int}/status"), SwaggerOperation(
        Summary = "Change document type status",
        Description = "**Change the approval status of a document type.**<br/><br/>📋 **Status transitions:**<br/>• Unapproved → Approved<br/>• Approved → Frozen<br/>• Frozen → Approved<br/><br/>🎯 **Use this to:** Approve new types, freeze unused types, reactivate frozen types")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ChangeDocumentTypeStatus(
        int id,
        [FromBody] ChangeDocumentTypeStatusRequest request,
        CancellationToken cancellationToken)
    {
        // Create command with ID from route and status from request
        var command = new ChangeDocumentTypeStatusCommand(id, request.Status);
        var result = await mediator.Send(command, cancellationToken);

        return result.ToActionResult();
    }
}
