namespace Docman.Core.Domain.Enums;

/// <summary>
/// Represents the different status values for a building
/// </summary>
public enum BuildingStatus
{
    /// <summary>
    /// Building is newly added or modified and awaiting approval
    /// </summary>
    Unapproved = 0,

    /// <summary>
    /// Building is confirmed and accepted
    /// </summary>
    Approved = 1,

    /// <summary>
    /// Building is inactive or frozen
    /// </summary>
    Frozen = 2
}
