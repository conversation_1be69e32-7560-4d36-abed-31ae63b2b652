namespace Docman.Infrastructure.Persistence.Configurations;

/// <summary>
/// EF Core configuration for Stock entity
/// </summary>
public class StockConfiguration : IEntityTypeConfiguration<Stock>
{
    public void Configure(EntityTypeBuilder<Stock> builder)
    {
        builder.HasKey(s => s.Id);

        builder.Property(s => s.Name)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(s => s.ManagerName)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(s => s.VillageCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(s => s.Address)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(s => s.MaintenancePhone)
            .HasMaxLength(20);

        builder.Property(s => s.SecurityPhone)
            .HasMaxLength(20);

        builder.Property(s => s.Email)
            .HasMaxLength(100);

        builder.Property(s => s.Description)
            .HasMaxLength(1000);

        builder.Property(s => s.Status)
            .HasConversion(
                v => v.ToString(),
                v => Enum.Parse<StockStatus>(v))
            .IsRequired();

        // Foreign key relationship with Village (DistrictOrVillage)
        builder.HasOne(s => s.Village)
            .WithMany()
            .HasForeignKey(s => s.VillageCode)
            .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete

        // One-to-many relationship with StockPhones
        builder.HasMany(s => s.StockPhones)
            .WithOne(sp => sp.Stock)
            .HasForeignKey(sp => sp.StockId)
            .OnDelete(DeleteBehavior.Cascade); // Cascade delete for dependent entities

        builder.ToTable("Stocks");
    }
}
