namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.DeleteOrganizationalNode;

/// <summary>
/// Validator for DeleteOrganizationalNodeCommand
/// </summary>
public sealed class DeleteOrganizationalNodeCommandValidator : AbstractValidator<DeleteOrganizationalNodeCommand>
{
    public DeleteOrganizationalNodeCommandValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Code is required.")
            .Must(OrganizationalCodeHelper.IsValidCode)
            .WithMessage("Code must be exactly 5 characters using Base-36 format (0-9, A-Z).");
    }
}
