namespace Docman.Core.Domain.Enums;

/// <summary>
/// Represents the different status values for a document type
/// </summary>
public enum DocumentTypeStatus
{
    /// <summary>
    /// Document type is newly created and awaiting approval
    /// </summary>
    Unapproved = 0,

    /// <summary>
    /// Document type is confirmed and approved for use
    /// </summary>
    Approved = 1,

    /// <summary>
    /// Document type is inactive and not allowed for use
    /// </summary>
    Frozen = 2
}
