namespace Docman.Infrastructure.Persistence.Repositories.Specifications;

/// <summary>
/// Repository implementation for organizational node operations with hierarchy-specific functionality
/// </summary>
public class OrganizationalNodeRepository(DocmanDbContext context)
    : GenericRepository<OrganizationalNode>(context), IOrganizationalNodeRepository
{
    public async Task<List<OrganizationalNode>> GetNodesByLevelAsync(
        OrganizationalLevel level,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        return await query
            .Where(x => x.Level == level)
            .OrderBy(x => x.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<OrganizationalNode?> GetByCodeAsync(
        string code,
        bool includeChildren = false,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        if (includeChildren)
            query = query.Include(x => x.Children);


        return await query.FirstOrDefaultAsync(x => x.Code == code, cancellationToken);
    }

    public async Task<List<OrganizationalNode>> GetChildrenAsync(
        string parentCode,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        return await query
            .Where(x => x.ParentCode == parentCode)
            .OrderBy(x => x.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<OrganizationalNode>> GetDescendantsAsync(
        string parentCode,
        CancellationToken cancellationToken = default)
    {
        var descendants = new List<OrganizationalNode>();
        var currentLevelCodes = new List<string> { parentCode };

        while (currentLevelCodes.Count > 0)
        {
            var nextLevelCodes = new List<string>();

            foreach (var code in currentLevelCodes)
            {
                var children = await GetChildrenAsync(code, cancellationToken);
                descendants.AddRange(children);
                nextLevelCodes.AddRange(children.Select(c => c.Code));
            }

            currentLevelCodes = nextLevelCodes;
        }

        return descendants.OrderBy(d => d.Code).ToList();
    }

    public async Task<List<OrganizationalNode>> GetPathToRootAsync(
        string code,
        CancellationToken cancellationToken = default)
    {
        var path = new List<OrganizationalNode>();
        var currentCode = code;

        while (!string.IsNullOrEmpty(currentCode))
        {
            var node = await DbSet
                .FirstOrDefaultAsync(x => x.Code == currentCode, cancellationToken);

            if (node is null)
                break;

            path.Insert(0, node);
            currentCode = node.ParentCode;
        }

        return path;
    }

    public async Task<List<OrganizationalNode>> GetCompleteTreeAsync(
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        return await query
            .OrderBy(x => x.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> HasChildrenAsync(
        string parentCode,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        return await query.AnyAsync(x => x.ParentCode == parentCode, cancellationToken);
    }

    public async Task<Dictionary<OrganizationalLevel, int>> GetLevelCountsAsync(
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        var counts = await query
            .GroupBy(x => x.Level)
            .Select(g => new { Level = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);

        return counts.ToDictionary(x => x.Level, x => x.Count);
    }

    public async Task<HashSet<string>> GetAllExistingCodesAsync(CancellationToken cancellationToken = default)
    {
        var codes = await DbSet
            .Select(x => x.Code)
            .ToListAsync(cancellationToken);

        return new HashSet<string>(codes, StringComparer.OrdinalIgnoreCase);
    }

    public async Task<bool> CodeExistsAsync(string code, CancellationToken cancellationToken = default)
    {
        return await DbSet
            .AnyAsync(x => x.Code == code, cancellationToken);
    }

    public async Task<List<OrganizationalNode>> SearchByNameAsync(
        string namePattern,
        CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        return await query
            .Where(x => EF.Functions.Like(x.Name, $"%{namePattern}%"))
            .OrderBy(x => x.Code)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ValidateParentChildRelationshipAsync(
        string childCode,
        string? parentCode,
        CancellationToken cancellationToken = default)
    {
        if (!OrganizationalCodeHelper.IsValidCode(childCode))
            return false;

        if (string.IsNullOrEmpty(parentCode))
        {
            // Child should be L1
            return OrganizationalCodeHelper.DetermineLevel(childCode) == OrganizationalLevel.L1;
        }

        if (!OrganizationalCodeHelper.IsValidCode(parentCode))
            return false;

        // Check if parent exists
        var parentExists = await CodeExistsAsync(parentCode, cancellationToken);

        return parentExists &&
               // Check if the relationship is valid based on code rules
               OrganizationalCodeHelper.CanBeChildOf(childCode, parentCode);
    }

    public async Task<int> HardDeleteAsync(
        string code,
        bool cascadeDelete = false,
        CancellationToken cancellationToken = default)
    {
        var deletedCount = 0;

        if (cascadeDelete)
        {
            // Get all descendants first
            var descendants = await GetDescendantsAsync(code, cancellationToken);

            // Delete descendants first (bottom-up)
            var descendantsByLevel = descendants
                .GroupBy(d => d.Level)
                .OrderByDescending(g => g.Key); // L4, L3, L2, L1

            foreach (var levelGroup in descendantsByLevel)
            {
                foreach (var node in levelGroup)
                {
                    DbSet.Remove(node);
                    deletedCount++;
                }
            }
        }

        // Delete the target node
        var targetNode = await DbSet
            .FirstOrDefaultAsync(x => x.Code == code, cancellationToken);

        if (targetNode == null)
            return deletedCount;
        
        DbSet.Remove(targetNode);
        deletedCount++;

        return deletedCount;
    }
}