﻿namespace Docman.Infrastructure.Persistence.Configurations;

public class CityOrDistrictConfiguration : IEntityTypeConfiguration<CityOrDistrict>
{
    public void Configure(EntityTypeBuilder<CityOrDistrict> builder)
    {
        builder.HasKey(cd => cd.Code);

        builder.Property(cd => cd.Code)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(cd => cd.NameAr)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(cd => cd.NameEn)
            .HasMaxLength(200);

        builder.Property(cd => cd.GovernorateCode)
            .HasMaxLength(10)
            .IsRequired();

        builder.HasMany(cd => cd.DistrictOrVillages)
            .WithOne(dv => dv.CityOrDistrict)
            .HasForeignKey(dv => dv.CityOrDistrictCode)
            .OnDelete(DeleteBehavior.Cascade);
    }
}