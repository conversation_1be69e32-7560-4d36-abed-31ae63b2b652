using Docman.Core.Application.Common.DTOs.Stocks;

namespace Docman.Core.Application.Features.Stocks.Commands.UpdateStock;

/// <summary>
/// Command to update an existing stock
/// </summary>
public sealed record UpdateStockCommand(
    int Id,
    string Name,
    string ManagerName,
    string VillageCode,
    string Address,
    string? MaintenancePhone,
    string? SecurityPhone,
    string? Email,
    string? Description,
    ICollection<CreateStockPhoneRequest> StockPhones
) : IRequest<Result<StockDetailsDto>>;
