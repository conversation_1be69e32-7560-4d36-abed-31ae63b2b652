namespace Docman.Core.Application.Features.Stocks.Queries.GetStockDetails;

/// <summary>
/// Validator for GetStockDetailsQuery
/// </summary>
public sealed class GetStockDetailsQueryValidator : AbstractValidator<GetStockDetailsQuery>
{
    public GetStockDetailsQueryValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Stock ID must be greater than 0");
    }
}
