using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Commands.UpdateOrganizationalNode;

/// <summary>
/// Hand<PERSON> for updating organizational nodes while maintaining hierarchy integrity
/// </summary>
public sealed class UpdateOrganizationalNodeCommandHandler(
    IOrganizationalNodeRepository organizationalNodeRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<UpdateOrganizationalNodeCommand, Result<OrganizationalNodeDto>>
{
    public async Task<Result<OrganizationalNodeDto>> Handle(
        UpdateOrganizationalNodeCommand request,
        CancellationToken cancellationToken)
    {
        // Get the existing node
        var existingNode = await organizationalNodeRepository.GetByCodeAsync(
            request.Code,
            cancellationToken: cancellationToken);

        if (existingNode is null)
            return Result.Failure<OrganizationalNodeDto>(OrganizationalNodeErrors.NodeNotFound);

        // Check for duplicate names at the same level (excluding the current node)
        var siblingsWithSameName = await GetSiblingsWithNameAsync(
            request.Name,
            existingNode.ParentCode,
            request.Code,
            cancellationToken);

        if (siblingsWithSameName.Count != 0)
            return Result.Failure<OrganizationalNodeDto>(OrganizationalNodeErrors.DuplicateNameInLevel);

        // Update the node properties
        existingNode.Name = request.Name.Trim();
        existingNode.Description = request.Description?.Trim();

        // Save changes
        await organizationalNodeRepository.UpdateAsync(existingNode, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        // Map to DTO and return
        var result = existingNode.Adapt<OrganizationalNodeDto>();
        return Result.Success(result);
    }

    /// <summary>
    /// Gets siblings with the same name to prevent duplicates at the same level
    /// </summary>
    private async Task<List<OrganizationalNode>> GetSiblingsWithNameAsync(
        string name,
        string? parentCode,
        string excludeCode,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(parentCode))
        {
            // For L1 nodes, check other L1 nodes
            var l1Nodes = await organizationalNodeRepository.GetNodesByLevelAsync(
                OrganizationalLevel.L1,
                cancellationToken);

            return l1Nodes
                .Where(n => !string.Equals(n.Code, excludeCode, StringComparison.OrdinalIgnoreCase))
                .Where(n => string.Equals(n.Name, name, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        // For other levels, check siblings
        var siblings = await organizationalNodeRepository.GetChildrenAsync(
            parentCode,
            cancellationToken);

        return siblings
            .Where(n => !string.Equals(n.Code, excludeCode, StringComparison.OrdinalIgnoreCase))
            .Where(n => string.Equals(n.Name, name, StringComparison.OrdinalIgnoreCase))
            .ToList();
    }


}
