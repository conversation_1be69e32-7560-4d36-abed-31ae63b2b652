namespace Docman.Core.Application.Features.DocumentTypes.Queries.GetDocumentTypeById;

/// <summary>
/// Validator for GetDocumentTypeByIdQuery
/// </summary>
public sealed class GetDocumentTypeByIdQueryValidator : AbstractValidator<GetDocumentTypeByIdQuery>
{
    public GetDocumentTypeByIdQueryValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Document type ID must be a positive number.");
    }
}
