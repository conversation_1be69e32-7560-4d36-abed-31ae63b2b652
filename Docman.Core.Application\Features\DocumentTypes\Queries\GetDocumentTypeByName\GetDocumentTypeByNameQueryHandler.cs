using Docman.Core.Application.Common.DTOs.DocumentTypes;
using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.DocumentTypes.Queries.GetDocumentTypeByName;

/// <summary>
/// Handler for GetDocumentTypeByNameQuery
/// </summary>
public sealed class GetDocumentTypeByNameQueryHandler(
    IGenericRepository<DocumentType> documentTypeRepository
) : IRequestHandler<GetDocumentTypeByNameQuery, Result<DocumentTypeDetailsDto>>
{
    public async Task<Result<DocumentTypeDetailsDto>> Handle(GetDocumentTypeByNameQuery request, CancellationToken cancellationToken)
    {
        var documentType = await documentTypeRepository.GetAsync(
            filter: dt => dt.Name == request.Name,
            cancellationToken: cancellationToken);

        if (documentType == null)
        {
            return Result.Failure<DocumentTypeDetailsDto>(DocumentTypeErrors.DocumentTypeNotFound);
        }

        var documentTypeDto = documentType.Adapt<DocumentTypeDetailsDto>();

        return Result.Success(documentTypeDto);
    }
}
