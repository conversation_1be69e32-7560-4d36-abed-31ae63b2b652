namespace Docman.Core.Application.Features.Stocks.Commands.CreateStock;

/// <summary>
/// Handler for CreateStockCommand
/// </summary>
public sealed class CreateStockCommandHandler(
    IGenericRepository<Stock> stockRepository,
    IGenericRepository<DistrictOrVillage> villageRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<CreateStockCommand, Result<StockDetailsDto>>
{
    public async Task<Result<StockDetailsDto>> Handle(CreateStockCommand request, CancellationToken cancellationToken)
    {
        // Sanitize input data
        var name = request.Name.Trim();
        var managerName = request.ManagerName.Trim();
        var villageCode = request.VillageCode.Trim();
        var address = request.Address.Trim();

        // Validate village code exists
        var village = await villageRepository.GetAsync(
            filter: v => v.Code == villageCode,
            cancellationToken: cancellationToken);
        if (village == null)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.VillageNotFound);
        }


        // Check for duplicate stock name in the same village
        var duplicateExists = await stockRepository.AnyAsync(
            s => s.Name.ToLower() == name.ToLower() &&
                 s.VillageCode == villageCode &&
                 !s.IsDeleted,
            cancellationToken);
        if (duplicateExists)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.StockAlreadyExists);
        }

        // Create stock entity
        var stock = new Stock
        {
            Name = name,
            ManagerName = managerName,
            VillageCode = villageCode,
            Address = address,
            MaintenancePhone = request.MaintenancePhone?.Trim(),
            SecurityPhone = request.SecurityPhone?.Trim(),
            Email = request.Email?.Trim(),
            Description = request.Description?.Trim(),
            Status = StockStatus.Unapproved
        };

        // Add stock phones if provided
        if (request.StockPhones?.Any() == true)
        {
            foreach (var phoneRequest in request.StockPhones)
            {
                var stockPhone = new StockPhone
                {
                    PhoneNumber = phoneRequest.PhoneNumber.Trim(),
                    Notes = phoneRequest.Notes?.Trim()
                };
                stock.StockPhones.Add(stockPhone);
            }
        }

        // Add to repository
        var addedStock = await stockRepository.AddAsync(stock, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        // Get the complete stock details to return
        var stockDetails = await stockRepository.GetAsync(
            filter: s => s.Id == addedStock.Id && !s.IsDeleted,
            includeProperties: ["Village.CityOrDistrict.Governorate.Country", "StockPhones"],
            cancellationToken: cancellationToken);

        if (stockDetails == null)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.StockNotFound);
        }

        // Map to DTO
        var result = stockDetails.Adapt<StockDetailsDto>();
        return Result.Success(result);
    }
}