﻿using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Locations.Queries.GetGovernoratesByCountryQuery;

public class GetGovernoratesByCountryQueryHandler(
    IGenericRepository<Country> countryRepository,
    IGenericRepository<Governorate> governorateRepository)
    : IRequestHandler<GetGovernoratesByCountryQuery, Result<GetGovernoratesByCountryResponse>>
{
    public async Task<Result<GetGovernoratesByCountryResponse>> Handle(GetGovernoratesByCountryQuery request,
        CancellationToken cancellationToken)
    {
        // Check if country exists
        var countryExists = await countryRepository.AnyAsync(
            c => c.Code == request.CountryCode,
            cancellationToken);

        if (!countryExists)
        {
            return Result.Failure<GetGovernoratesByCountryResponse>(LocationErrors.CountryNotFound);
        }

        var governorates = await governorateRepository.GetAllAsync(
            filter: g => g.CountryCode == request.CountryCode,
            tracked: false,
            cancellationToken: cancellationToken);

        var governorateDtos = governorates.Adapt<List<GovernorateDto>>();

        return Result.Success(new GetGovernoratesByCountryResponse(governorateDtos));
    }
}