namespace Docman.Core.Domain.Entities;

/// <summary>
/// Represents a phone number associated with a stock
/// </summary>
public class StockPhone
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Foreign key to Stock (required)
    /// </summary>
    public int StockId { get; set; }

    /// <summary>
    /// The contact number (required)
    /// </summary>
    public string PhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes about the number (optional)
    /// </summary>
    public string? Notes { get; set; }

    // Navigation property
    /// <summary>
    /// Navigation property to the parent Stock
    /// </summary>
    public Stock Stock { get; set; } = null!;
}