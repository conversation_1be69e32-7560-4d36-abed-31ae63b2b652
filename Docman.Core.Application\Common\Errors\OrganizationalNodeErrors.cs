namespace Docman.Core.Application.Common.Errors;

/// <summary>
/// Static class containing all organizational node related errors
/// </summary>
public static class OrganizationalNodeErrors
{
    // Code validation errors
    public static readonly Error InvalidCodeFormat = new(
        "OrganizationalNode.InvalidCodeFormat",
        "Organizational node code must be exactly 5 characters using Base-36 format (0-9, A-Z).");

    public static readonly Error InvalidCodeHierarchy = new(
        "OrganizationalNode.InvalidCodeHierarchy",
        "Code does not follow organizational hierarchy rules. First character must be '1'.");

    public static readonly Error CodeAlreadyExists = new(
        "OrganizationalNode.CodeAlreadyExists",
        "An organizational node with this code already exists.");

    // Hierarchy validation errors
    public static readonly Error ParentNotFound = new(
        "OrganizationalNode.ParentNotFound",
        "The specified parent organizational node does not exist.");

    public static readonly Error InvalidParentChild = new(
        "OrganizationalNode.InvalidParentChild",
        "The parent-child relationship is invalid based on code hierarchy rules.");

    public static readonly Error OrphanNode = new(
        "OrganizationalNode.OrphanNode",
        "Cannot create node without a valid parent (except for L1 nodes).");

    public static readonly Error MaxLevelExceeded = new(
        "OrganizationalNode.MaxLevelExceeded",
        "Maximum organizational level (L4) has been reached.");

    // Node operations errors
    public static readonly Error NodeNotFound = new(
        "OrganizationalNode.NotFound",
        "Organizational node was not found.");

    public static readonly Error CannotDeleteWithChildren = new(
        "OrganizationalNode.CannotDeleteWithChildren",
        "Cannot delete organizational node that has child nodes. Delete children first or use cascade delete.");

    public static readonly Error NodeAlreadyDeleted = new(
        "OrganizationalNode.AlreadyDeleted",
        "Organizational node has already been deleted.");

    public static readonly Error NodeNotActive = new(
        "OrganizationalNode.NotActive",
        "Organizational node is not active.");

    // Name validation errors
    public static readonly Error EmptyName = new(
        "OrganizationalNode.EmptyName",
        "Organizational node name cannot be empty.");

    public static readonly Error NameTooLong = new(
        "OrganizationalNode.NameTooLong",
        "Organizational node name cannot exceed 200 characters.");

    public static readonly Error DuplicateNameInLevel = new(
        "OrganizationalNode.DuplicateNameInLevel",
        "An organizational node with this name already exists at the same level.");

    // Code generation errors
    public static readonly Error NoAvailableCodes = new(
        "OrganizationalNode.NoAvailableCodes",
        "No available codes at the specified level. Maximum capacity reached.");

    public static readonly Error InvalidLevel = new(
        "OrganizationalNode.InvalidLevel",
        "Invalid organizational level specified. Level must be L1, L2, L3, or L4.");

    // Move operation errors
    public static readonly Error CannotMoveToDescendant = new(
        "OrganizationalNode.CannotMoveToDescendant",
        "Cannot move organizational node to one of its descendants.");

    public static readonly Error CannotMoveToSelf = new(
        "OrganizationalNode.CannotMoveToSelf",
        "Cannot move organizational node to itself.");

    // Bulk operation errors
    public static readonly Error BulkOperationFailed = new(
        "OrganizationalNode.BulkOperationFailed",
        "Bulk operation failed. Some nodes could not be processed.");

    // Permission errors
    public static readonly Error InsufficientPermissions = new(
        "OrganizationalNode.InsufficientPermissions",
        "Insufficient permissions to perform this operation on organizational structure.");

    public static readonly Error RestrictedLevelOperation = new(
        "OrganizationalNode.RestrictedLevelOperation",
        "Operations on high-level nodes (L1, L2) require special permissions.");
}
