namespace Docman.Core.Application.Features.Stocks.Commands.UpdateStock;

/// <summary>
/// Handler for UpdateStockCommand
/// </summary>
public sealed class UpdateStockCommandHandler(
    IGenericRepository<Stock> stockRepository,
    IGenericRepository<DistrictOrVillage> villageRepository,
    IUnitOfWork unitOfWork
) : IRequestHandler<UpdateStockCommand, Result<StockDetailsDto>>
{
    public async Task<Result<StockDetailsDto>> Handle(UpdateStockCommand request, CancellationToken cancellationToken)
    {
        // Get the existing stock
        var existingStock = await stockRepository.GetAsync(
            filter: s => s.Id == request.Id && !s.IsDeleted,
            includeProperties: ["StockPhones"],
            cancellationToken: cancellationToken);
        if (existingStock == null)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.StockNotFound);
        }

        // Sanitize input data
        var name = request.Name.Trim();
        var managerName = request.ManagerName.Trim();
        var villageCode = request.VillageCode.Trim();
        var address = request.Address.Trim();

        // Validate village code exists
        var village = await villageRepository.GetAsync(
            filter: v => v.Code == villageCode,
            cancellationToken: cancellationToken);
        if (village == null)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.VillageNotFound);
        }

        // Check for duplicate stock name in the same village (excluding current stock)
        var duplicateExists = await stockRepository.AnyAsync(
            s => s.Name.ToLower() == name.ToLower() &&
                 s.VillageCode == villageCode &&
                 s.Id != request.Id &&
                 !s.IsDeleted,
            cancellationToken);

        if (duplicateExists)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.StockAlreadyExists);
        }

        // Update stock properties
        existingStock.Name = name;
        existingStock.ManagerName = managerName;
        existingStock.VillageCode = villageCode;
        existingStock.Address = address;
        existingStock.MaintenancePhone = request.MaintenancePhone?.Trim();
        existingStock.SecurityPhone = request.SecurityPhone?.Trim();
        existingStock.Email = request.Email?.Trim();
        existingStock.Description = request.Description?.Trim();

        // Clear existing stock phones and add new ones
        existingStock.StockPhones.Clear();
        if (request.StockPhones?.Any() == true)
        {
            foreach (var phoneRequest in request.StockPhones)
            {
                var stockPhone = new StockPhone
                {
                    PhoneNumber = phoneRequest.PhoneNumber.Trim(),
                    Notes = phoneRequest.Notes?.Trim()
                };
                existingStock.StockPhones.Add(stockPhone);
            }
        }

        // Reset status to Unapproved when updating
        existingStock.Status = StockStatus.Unapproved;

        // Update the stock
        await stockRepository.UpdateAsync(existingStock, cancellationToken);

        // Save changes
        await unitOfWork.SaveChangesAsync(cancellationToken);

        // Get the updated stock details to return
        var updatedStock = await stockRepository.GetAsync(
            filter: s => s.Id == request.Id && !s.IsDeleted,
            includeProperties: ["Village.CityOrDistrict.Governorate.Country", "StockPhones"],
            cancellationToken: cancellationToken);

        if (updatedStock == null)
        {
            return Result.Failure<StockDetailsDto>(StockErrors.StockNotFound);
        }

        // Map to DTO
        var result = updatedStock.Adapt<StockDetailsDto>();
        return Result.Success(result);
    }
}