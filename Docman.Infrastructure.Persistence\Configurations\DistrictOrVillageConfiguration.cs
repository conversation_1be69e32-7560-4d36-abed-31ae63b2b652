﻿namespace Docman.Infrastructure.Persistence.Configurations;

public class DistrictOrVillageConfiguration : IEntityTypeConfiguration<DistrictOrVillage>
{
    public void Configure(EntityTypeBuilder<DistrictOrVillage> builder)
    {
        builder.HasKey(dv => dv.Code);

        builder.Property(dv => dv.Code)
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(dv => dv.NameAr)
            .HasMaxLength(200)
            .IsRequired();

        builder.Property(dv => dv.CityOrDistrictCode)
            .HasMaxLength(10)
            .IsRequired();
    }
}