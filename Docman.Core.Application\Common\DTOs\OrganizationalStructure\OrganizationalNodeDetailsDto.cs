namespace Docman.Core.Application.Common.DTOs.OrganizationalStructure;

/// <summary>
/// Detailed data transfer object for organizational node with hierarchy information
/// </summary>
public class OrganizationalNodeDetailsDto : OrganizationalNodeDto
{
    /// <summary>
    /// Parent node information (null for L1)
    /// </summary>
    public OrganizationalNodeDto? Parent { get; set; }

    /// <summary>
    /// Child nodes
    /// </summary>
    public List<OrganizationalNodeDto> Children { get; set; } = new();

    /// <summary>
    /// Full path from root to this node
    /// </summary>
    public List<string> PathToRoot { get; set; } = new();

    /// <summary>
    /// Count of direct children
    /// </summary>
    public int ChildrenCount { get; set; }

    /// <summary>
    /// Count of all descendants
    /// </summary>
    public int DescendantsCount { get; set; }
}
