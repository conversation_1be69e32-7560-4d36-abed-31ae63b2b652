﻿namespace Docman.Core.Application.Contracts.Specifications;

public interface IAuthRepository
{
    Task<Result<AuthResponse>> GetTokenAsync(string email, string password, CancellationToken cancellation);
    Task<Result<AuthResponse>> GetRefreshTokenAsync(string token, string refreshToken, CancellationToken cancellation);
    Task<bool> RevokeRefreshTokenAsync(string token, string refreshToken, CancellationToken cancellation);
}
