﻿using Docman.Core.Application;
using Docman.Infrastructure.Persistence;
using Docman.Infrastructure.Integration;
using Docman.Presentation.API.Exceptions;
using Docman.Presentation.API.Swagger;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Docman.Presentation.API;

public static class DependencyInjection
{
    public static IServiceCollection AddApiDependencies(this IServiceCollection services,
        IConfiguration configuration)

    {
        // Add services to the container
        services.AddControllers();
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();
        services.AddProblemDetails();
        services.AddExceptionHandler<GlobalExceptionHandler>();
        services.AddHttpContextAccessor();

        // Add Clean Architecture layers dependencies
        services.AddLayersDependencies(configuration);

        services.AddCorsConfigurations();
        services.AddHealthChecksConfigurations();
        services.AddSwaggerServices();

        return services;
    }

    private static IServiceCollection AddSwaggerServices(this IServiceCollection services)
    {
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();

        services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();

        return services;
    }
    private static IServiceCollection AddLayersDependencies(this IServiceCollection services,
        IConfiguration configuration)
    {
        services
            .AddApplication()
            .AddPersistenceDependencies(configuration)
            .AddIntegration(configuration);

        return services;
    }

    private static IServiceCollection AddCorsConfigurations(this IServiceCollection services)
    {
        services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy.AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });
        });

        return services;
    }
    

    private static IServiceCollection AddHealthChecksConfigurations(this IServiceCollection services)
    {
        services.AddHealthChecks();
        
        //todo: Add specific health checks later
        
        return services;
    }
    
}