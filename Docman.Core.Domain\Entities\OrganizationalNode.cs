namespace Docman.Core.Domain.Entities;

/// <summary>
/// Represents a node in the organizational hierarchy with automatic level detection
/// and parent-child relationship derivation based on a 5-character Base-36 code.
/// </summary>
public class OrganizationalNode
{
    /// <summary>
    /// Primary Key: 5-character Base-36 code (0-9, A-Z)
    /// Format: D1D2D3D4D5 where D1 is always "1" in current hierarchy
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// Display name of the organizational node
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the organizational node
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Organizational level derived from the code pattern
    /// </summary>
    public OrganizationalLevel Level { get; set; }

    /// <summary>
    /// Parent code derived from the current code based on level rules
    /// Stored for performance but always computed from the code
    /// </summary>
    public string? ParentCode { get; set; }

    /// <summary>
    /// Navigation property to parent node
    /// </summary>
    public OrganizationalNode? Parent { get; set; }

    /// <summary>
    /// Navigation property to child nodes
    /// </summary>
    public ICollection<OrganizationalNode> Children { get; set; } = new List<OrganizationalNode>();
}
