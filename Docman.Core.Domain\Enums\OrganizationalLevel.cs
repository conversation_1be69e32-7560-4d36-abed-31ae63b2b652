namespace Docman.Core.Domain.Enums;

/// <summary>
/// Represents the hierarchical levels in the organizational structure.
/// Each level has specific Base-36 code patterns and validation rules.
/// </summary>
public enum OrganizationalLevel
{
    /// <summary>
    /// Level 1: Central Administration (Pattern: 1X000)
    /// Top level in the hierarchy. Code ends with 000.
    /// Example: 11000, 12000
    /// </summary>
    L1 = 1,

    /// <summary>
    /// Level 2: General Administrations/Branches (Pattern: XXY00)
    /// Child of L1. Code ends with 00 but not 000.
    /// Example: 11200, 11300
    /// </summary>
    L2 = 2,

    /// <summary>
    /// Level 3: Units (Pattern: XXXY0)
    /// Child of L2. Code ends with 0 but not 00.
    /// Example: 11130, 11140
    /// </summary>
    L3 = 3,

    /// <summary>
    /// Level 4: Sub-Units (Pattern: XXXXZ)
    /// Child of L3. Code does not end with 0.
    /// Example: 11131, 11132
    /// </summary>
    L4 = 4
}
