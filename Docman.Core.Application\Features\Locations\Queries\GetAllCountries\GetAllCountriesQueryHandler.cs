namespace Docman.Core.Application.Features.Locations.Queries.GetAllCountries;

public class GetAllCountriesQueryHandler(IGenericRepository<Country> countryRepository)
    : IRequestHandler<GetAllCountriesQuery, Result<GetAllCountriesResponse>>
{
    public async Task<Result<GetAllCountriesResponse>> Handle(GetAllCountriesQuery request, CancellationToken cancellationToken)
    {
        var countries = await countryRepository.GetAllAsync(
            tracked: false,
            cancellationToken: cancellationToken);

        var countryDtos = countries.Adapt<List<CountryDto>>();
        
        return Result.Success(new GetAllCountriesResponse(countryDtos));
    }
}
