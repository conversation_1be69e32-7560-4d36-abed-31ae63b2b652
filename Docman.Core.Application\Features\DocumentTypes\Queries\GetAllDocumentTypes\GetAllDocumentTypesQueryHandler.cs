using Docman.Core.Application.Common.DTOs.DocumentTypes;

namespace Docman.Core.Application.Features.DocumentTypes.Queries.GetAllDocumentTypes;

/// <summary>
/// Handler for GetAllDocumentTypesQuery
/// </summary>
public sealed class GetAllDocumentTypesQueryHandler(
    IGenericRepository<DocumentType> documentTypeRepository
) : IRequestHandler<GetAllDocumentTypesQuery, Result<IEnumerable<DocumentTypeListDto>>>
{
    public async Task<Result<IEnumerable<DocumentTypeListDto>>> Handle(GetAllDocumentTypesQuery request, CancellationToken cancellationToken)
    {
        var documentTypes = await documentTypeRepository.GetAllAsync(cancellationToken: cancellationToken);

        var documentTypeDtos = documentTypes.Adapt<IEnumerable<DocumentTypeListDto>>();

        return Result.Success(documentTypeDtos);
    }
}
