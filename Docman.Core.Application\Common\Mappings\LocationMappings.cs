namespace Docman.Core.Application.Common.Mappings;

public class LocationMappings : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<Country, CountryDto>()
            .Map(dest => dest.Code, src => src.Code)
            .Map(dest => dest.NameAr, src => src.NameAr)
            .Map(dest => dest.NameEn, src => src.NameEn);

        config.NewConfig<Governorate, GovernorateDto>()
            .Map(dest => dest.Code, src => src.Code)
            .Map(dest => dest.NameAr, src => src.NameAr)
            .Map(dest => dest.NameEn, src => src.NameEn);

        config.NewConfig<CityOrDistrict, CityDto>()
            .Map(dest => dest.Code, src => src.Code)
            .Map(dest => dest.NameAr, src => src.NameAr)
            .Map(dest => dest.NameEn, src => src.NameEn);

        config.NewConfig<DistrictOrVillage, DistrictDto>()
            .Map(dest => dest.Code, src => src.Code)
            .Map(dest => dest.NameAr, src => src.NameAr);
    }
}