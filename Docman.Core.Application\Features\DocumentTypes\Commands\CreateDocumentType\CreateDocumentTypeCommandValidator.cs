namespace Docman.Core.Application.Features.DocumentTypes.Commands.CreateDocumentType;

/// <summary>
/// Validator for CreateDocumentTypeCommand
/// </summary>
public sealed class CreateDocumentTypeCommandValidator : AbstractValidator<CreateDocumentTypeCommand>
{
    public CreateDocumentTypeCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Document type name is required.")
            .MaximumLength(255)
            .WithMessage("Document type name must not exceed 255 characters.")
            .Matches(@"^[\u0600-\u06FFa-zA-Z0-9\s\-_\.]+$")
            .WithMessage("Document type name can only contain Arabic letters, English letters, numbers, spaces, hyphens, underscores, and dots.");
    }
}

