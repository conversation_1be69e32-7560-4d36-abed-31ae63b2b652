using Docman.Core.Application.Common.DTOs.Stocks;
using Docman.Core.Application.Features.Stocks.Commands.CreateStock;
using Docman.Core.Application.Features.Stocks.Commands.UpdateStock;
using Docman.Core.Application.Features.Stocks.Commands.DeleteStock;
using Docman.Core.Application.Features.Stocks.Commands.UpdateStockStatus;
using Docman.Core.Application.Features.Stocks.Queries.GetAllStocks;
using Docman.Core.Application.Features.Stocks.Queries.GetStockDetails;
using Mapster;

namespace Docman.Presentation.API.Controllers;

/// <summary>
/// Manage stock records - view, create, update, and delete stock information.
/// </summary>
/// <remarks>
/// 🔐 All endpoints require authentication (Bearer token).
/// 🏪 **Perfect for:** Stock management systems, warehouse management, inventory tracking.
/// 📍 Each stock has location details (country, city, village) and approval status.
/// 📞 Supports multiple phone numbers per stock for different purposes.
/// </remarks>
[SwaggerResponse(200, "Operation completed successfully")]
[SwaggerResponse(400, "Invalid request data")]
[SwaggerResponse(401, "User is not authorized")]
[SwaggerResponse(404, "Stock not found")]
[Authorize]
public class StocksController(IMediator mediator) : BaseController
{
    /// <summary>
    /// Get list of all stocks with basic information.
    /// </summary>
    /// <remarks>
    /// Returns a summary list of all stocks including:
    /// - Stock name and manager
    /// - Location hierarchy (Village → City → Governorate → Country)
    /// - Current approval status
    /// 
    /// **Perfect for:** Stock listing pages, dashboard views, selection dropdowns.
    /// </remarks>
    /// <response code="200">List of stocks retrieved successfully</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<StockListDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetAllStocks()
    {
        var query = new GetAllStocksQuery();
        var result = await mediator.Send(query);
        return result.ToActionResult();
    }

    /// <summary>
    /// Get detailed information for a specific stock by ID.
    /// </summary>
    /// <param name="id">The unique identifier of the stock</param>
    /// <remarks>
    /// Returns complete stock information including:
    /// - All stock properties (name, manager, address, etc.)
    /// - Complete location hierarchy details
    /// - All associated phone numbers
    /// - Audit information (created/updated timestamps and users)
    /// 
    /// **Perfect for:** Stock detail pages, edit forms, comprehensive stock views.
    /// </remarks>
    /// <response code="200">Stock details retrieved successfully</response>
    /// <response code="404">Stock not found</response>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(StockDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetStockDetails(int id)
    {
        var query = new GetStockDetailsQuery(id);
        var result = await mediator.Send(query);
        return result.ToActionResult();
    }

    /// <summary>
    /// Create a new stock record.
    /// </summary>
    /// <param name="request">Stock creation details</param>
    /// <remarks>
    /// Creates a new stock with:
    /// - Default status: **Unapproved** (requires manual approval later)
    /// - Required fields: Name, Manager, Village, Address
    /// - Optional contact information: Phones, Email, Description
    /// - Multiple phone numbers supported
    /// 
    /// **Business Rules:**
    /// - Stock name must be unique within the same village
    /// - Village code must exist in the system
    /// - All phone numbers are validated for format
    /// 
    /// **Perfect for:** Stock registration forms, warehouse setup, new location creation.
    /// </remarks>
    /// <response code="200">Stock created successfully</response>
    /// <response code="400">Invalid stock data or business rule violation</response>
    [HttpPost]
    [ProducesResponseType(typeof(StockDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateStock([FromBody] CreateStockRequest request)
    {
        var command = request.Adapt<CreateStockCommand>();

        var result = await mediator.Send(command);
        return result.ToActionResult();
    }

    /// <summary>
    /// Update an existing stock record.
    /// </summary>
    /// <param name="id">The unique identifier of the stock to update</param>
    /// <param name="request">Updated stock details</param>
    /// <remarks>
    /// Updates stock information and:
    /// - **Resets status to Unapproved** (requires re-approval after changes)
    /// - Validates new data against business rules
    /// - Replaces all phone numbers with the provided list
    /// 
    /// **Business Rules:**
    /// - Stock name must be unique within the same village (excluding current stock)
    /// - Village code must exist in the system
    /// - Cannot update non-existent stocks
    /// 
    /// **Perfect for:** Stock information updates, manager changes, contact updates.
    /// </remarks>
    /// <response code="200">Stock updated successfully</response>
    /// <response code="400">Invalid stock data or business rule violation</response>
    /// <response code="404">Stock not found</response>
    [HttpPut("{id:int}")]
    [ProducesResponseType(typeof(StockDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateStock(int id, [FromBody] UpdateStockRequest request)
    {
        var command = new UpdateStockCommand(
            id,
            request.Name,
            request.ManagerName,
            request.VillageCode,
            request.Address,
            request.MaintenancePhone,
            request.SecurityPhone,
            request.Email,
            request.Description,
            request.StockPhones
        );

        var result = await mediator.Send(command);
        return result.ToActionResult();
    }

    /// <summary>
    /// Delete a stock record (soft delete).
    /// </summary>
    /// <param name="id">The unique identifier of the stock to delete</param>
    /// <remarks>
    /// Performs a soft delete operation:
    /// - Stock is marked as deleted but not physically removed
    /// - Maintains data integrity and audit trail
    /// - Deleted stocks won't appear in normal queries
    /// 
    /// **Perfect for:** Decommissioning stocks, temporary removal, data cleanup.
    /// </remarks>
    /// <response code="200">Stock deleted successfully</response>
    /// <response code="404">Stock not found</response>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteStock(int id)
    {
        var command = new DeleteStockCommand(id);
        var result = await mediator.Send(command);
        return result.ToActionResult();
    }

    /// <summary>
    /// Update the approval status of a stock.
    /// </summary>
    /// <param name="id">The unique identifier of the stock to update</param>
    /// <param name="request">The new status information</param>
    /// <remarks>
    /// Updates the stock status following business workflow rules:
    /// 
    /// **Status codes:**
    /// - **Unapproved** → 0
    /// - **Approved** → 1
    /// - **Frozen** → 2
    /// 
    /// **Status Meanings:**
    /// - **Unapproved**: Default for new/modified stocks (awaiting approval)
    /// - **Approved**: Confirmed and accepted for use
    /// - **Frozen**: Inactive or suspended
    /// 
    /// **Perfect for:** Approval workflows, stock management, administrative actions.
    /// </remarks>
    /// <response code="200">Stock status updated successfully</response>
    /// <response code="400">Invalid status transition or request data</response>
    /// <response code="404">Stock not found</response>
    [HttpPatch("{id:int}/status")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateStockStatus(int id, [FromBody] UpdateStockStatusRequest request)
    {
        var command = new UpdateStockStatusCommand(id, request.NewStatus);
        var result = await mediator.Send(command);
        return result.ToActionResult();
    }
}