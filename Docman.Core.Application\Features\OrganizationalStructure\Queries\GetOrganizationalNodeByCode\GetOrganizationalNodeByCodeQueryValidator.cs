﻿namespace Docman.Core.Application.Features.OrganizationalStructure.Queries.GetOrganizationalNodeByCode;

public class GetOrganizationalNodeByCodeQueryValidator: AbstractValidator<GetOrganizationalNodeByCodeQuery>
{
    public GetOrganizationalNodeByCodeQueryValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("Code is required.")
            .Must(OrganizationalCodeHelper.IsValidCode)
            .WithMessage("Code must be exactly 5 characters using Base-36 format (0-9, A-Z).");
    }
}