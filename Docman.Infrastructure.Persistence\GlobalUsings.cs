global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.EntityFrameworkCore.Metadata.Builders;
global using Docman.Core.Domain.Entities;
global using Docman.Core.Domain.Common;
global using Docman.Core.Domain.Enums;
global using Docman.Core.Application.Services;
global using Docman.Infrastructure.Persistence.Seeds;
global using Docman.Infrastructure.Persistence.Context;
global using Docman.Core.Application.Contracts;
global using Microsoft.AspNetCore.Identity;
global using Docman.Core.Application.Contracts.Specifications;
global using System.Text;
