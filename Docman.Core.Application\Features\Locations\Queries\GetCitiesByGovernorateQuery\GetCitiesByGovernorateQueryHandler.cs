using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Locations.Queries.GetCitiesByGovernorateQuery;

public class GetCitiesByGovernorateQueryHandler(
    IGenericRepository<Governorate> governorateRepository,
    IGenericRepository<CityOrDistrict> cityRepository)
    : IRequestHandler<GetCitiesByGovernorateQuery, Result<GetCitiesByGovernorateResponse>>
{
    public async Task<Result<GetCitiesByGovernorateResponse>> Handle(GetCitiesByGovernorateQuery request, CancellationToken cancellationToken)
    {
        // Check if governorate exists
        var governorateExists = await governorateRepository.AnyAsync(
            g => g.Code == request.GovernorateCode,
            cancellationToken);

        if (!governorateExists)
        {
            return Result.Failure<GetCitiesByGovernorateResponse>(LocationErrors.GovernorateNotFound);
        }

        var cities = await cityRepository.GetAllAsync(
            filter: c => c.GovernorateCode == request.GovernorateCode,
            tracked: false,
            cancellationToken: cancellationToken);

        var cityDtos = cities.Adapt<List<CityDto>>();
        
        return Result.Success(new GetCitiesByGovernorateResponse(cityDtos));
    }
}
