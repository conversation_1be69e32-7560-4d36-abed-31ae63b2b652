using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Features.Buildings.Commands.UpdateBuilding;

/// <summary>
/// Command to update an existing building
/// </summary>
public sealed record UpdateBuildingCommand(
    int Id,
    string Name,
    string Address,
    string? Description,
    string? Email,
    string? MobileNumber,
    string DistrictOrVillageCode,
    BuildingStatus? Status
) : IRequest<Result<bool>>;
