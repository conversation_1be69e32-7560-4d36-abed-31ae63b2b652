// Global using statements for Application layer
global using System;
global using System.Collections.Generic;
global using System.Linq;
global using System.Threading;
global using System.Threading.Tasks;
global using MediatR;
global using Mapster;
global using FluentValidation;


global using Docman.Core.Application.Common.Results;
global using Docman.Core.Application.Contracts;
global using Docman.Core.Application.Services;
global using Docman.Core.Domain.Entities;
global using Docman.Core.Domain.Enums;
global using Docman.Core.Application.Common.DTOs.Stocks;
global using Docman.Core.Application.Common.DTOs;
global using Docman.Core.Application.Common.DTOs.LocationDtos;
global using Docman.Core.Application.Common.DTOs.OrganizationalStructure;
global using Docman.Core.Application.Common.Errors;


