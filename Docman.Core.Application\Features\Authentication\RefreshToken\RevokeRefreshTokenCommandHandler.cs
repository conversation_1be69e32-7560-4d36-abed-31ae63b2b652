﻿
using Docman.Core.Application.Contracts;
using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.Authentication.RefreshToken;

public sealed class RevokeRefreshTokenCommandHandler(IAuthRepository authRepository)
    :IRequestHandler<RevokeRefreshTokenCommand,Result<bool>>
{
    private readonly IAuthRepository _authRepository = authRepository;

    public async Task<Result<bool>> Handle(RevokeRefreshTokenCommand request, CancellationToken cancellationToken)
    {
        var result = await _authRepository.RevokeRefreshTokenAsync(request.Token, request.RefreshToken, cancellationToken);
        return result ? Result.Success(true) : Result.Failure<bool>(new Error("Failure","Failed to revoke refresh token"));
    }
}
