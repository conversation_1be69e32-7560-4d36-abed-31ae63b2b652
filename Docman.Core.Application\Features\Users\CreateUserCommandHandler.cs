﻿
using Docman.Core.Application.Contracts;
using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.Users;

public sealed class CreateUserCommandHandler(IUserRepository userRepository)
    :IRequestHandler<CreateUserCommand,Result<string>>
{
    private readonly IUserRepository _userRepository = userRepository;

    public async Task<Result<string>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        return await _userRepository.CreateUserAsync(request.FirstName,request.LastName,request.Email,request.Password,cancellationToken);
    }
}
