﻿using Docman.Core.Application.Common.Constants;
using Docman.Core.Application.Common.Errors;
using Docman.Core.Application.Common.Results;

namespace Docman.Infrastructure.Persistence.Repositories.Specifications;

public class UserRepository(UserManager<ApplicationUser> userManager) : IUserRepository
{
    private readonly UserManager<ApplicationUser> _userManager = userManager;

    public async Task<Result<string>> CreateUserAsync(string firstName, string lastName, string email, string password, CancellationToken cancellationToken = default)
    {
        var user = new ApplicationUser
        {
            UserName = email,
            Email = email,
            FirstName = firstName,
            LastName = lastName,
            MustChangePassword = true
        };

        var result = await _userManager.CreateAsync(user, password); // باسورد افتراضية
        if (!result.Succeeded)
            return Result.Failure<string>(new Error("User.CreationFailed","The user has not been created"));

        await _userManager.AddToRoleAsync(user, DefaultRoleNames.Employee);

        return Result.Success(user.Id);
    }
    public async Task<Result> ChangePasswordAsync(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken = default)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user is null)
            return Result.Failure(UserErrors.UserNotFound);

        var checkPassword = await _userManager.CheckPasswordAsync(user, currentPassword);
        if (!checkPassword)
            return Result.Failure(UserErrors.InvalidCredentials);

        var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);

        if (!result.Succeeded)
        {
            var error = result.Errors.First();

            return Result.Failure(new Error(error.Code, error.Description));
        }
        user.MustChangePassword = false;
        await _userManager.UpdateAsync(user);

        return Result.Success();
    }

}
