namespace Docman.Core.Application.Features.DocumentTypes.Commands.ChangeDocumentTypeStatus;

/// <summary>
/// Validator for ChangeDocumentTypeStatusCommand
/// </summary>
public sealed class ChangeDocumentTypeStatusCommandValidator : AbstractValidator<ChangeDocumentTypeStatusCommand>
{
    public ChangeDocumentTypeStatusCommandValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("Document type ID must be a positive number.");

        RuleFor(x => x.Status)
            .IsInEnum()
            .WithMessage("Status must be a valid DocumentTypeStatus value.");
    }
}

