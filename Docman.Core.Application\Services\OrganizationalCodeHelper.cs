using System.Text.RegularExpressions;

namespace Docman.Core.Application.Services;

/// <summary>
/// Application service for organizational code operations including parsing, validation, and hierarchy logic
/// </summary>
public static class OrganizationalCodeHelper
{
    /// <summary>
    /// Validates if the code follows the Base-36 format and organizational rules
    /// </summary>
    /// <param name="code">The code to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    public static bool IsValidCode(string? code)
    {
        // Must be exactly 5 characters, start with '1', and only contain 0-9 or A-Z
        return code is ['1', _, _, _, _]
               && Regex.IsMatch(code, "^[0-9A-Z]{5}$", RegexOptions.Compiled);
    }


    /// <summary>
    /// Determines the organizational level based on the code pattern
    /// </summary>
    /// <param name="code">The 5-character code</param>
    /// <returns>The organizational level</returns>
    public static OrganizationalLevel DetermineLevel(string code)
    {
        // Level detection based on ending pattern
        if (code.EndsWith("000"))
            return OrganizationalLevel.L1; // Ends with 000

        if (code.EndsWith("00"))
            return OrganizationalLevel.L2; // Ends with 00 (but not 000)

        if (code.EndsWith("0"))
            return OrganizationalLevel.L3; // Ends with 0 (but not 00)

        return OrganizationalLevel.L4; // Does not end with 0
    }

    /// <summary>
    /// Derives the parent code based on the current code and level rules
    /// </summary>
    /// <param name="code">The current node code</param>
    /// <returns>The parent code or null if this is a root node (L1)</returns>
    public static string? DeriveParentCode(string code)
    {
        if (!IsValidCode(code))
            throw new ArgumentException("Invalid code format", nameof(code));

        var level = DetermineLevel(code);

        return level switch
        {
            OrganizationalLevel.L1 => null, // No parent for L1
            OrganizationalLevel.L2 => code[..2] + "000", // First 2 chars + 000
            OrganizationalLevel.L3 => code[..3] + "00", // First 3 chars + 00
            OrganizationalLevel.L4 => code[..4] + "0", // First 4 chars + 0
            _ => throw new InvalidOperationException($"Unknown level: {level}")
        };
    }

    /// <summary>
    /// Gets the full path from a node to the root
    /// </summary>
    /// <param name="code">The node code</param>
    /// <returns>List of codes from root to current node</returns>
    public static List<string> GetPathToRoot(string code)
    {
        var path = new List<string>();
        var currentCode = code;

        while (!string.IsNullOrEmpty(currentCode))
        {
            path.Insert(0, currentCode);
            currentCode = DeriveParentCode(currentCode);
        }

        return path;
    }

    /// <summary>
    /// Validates if a node can be a child of the specified parent
    /// </summary>
    /// <param name="childCode">The child node code</param>
    /// <param name="parentCode">The potential parent code</param>
    /// <returns>True if valid parent-child relationship</returns>
    public static bool CanBeChildOf(string childCode, string? parentCode)
    {
        if (!IsValidCode(childCode))
            return false;

        var childLevel = DetermineLevel(childCode);

        if (string.IsNullOrWhiteSpace(parentCode))
            return childLevel == OrganizationalLevel.L1;

        if (!IsValidCode(parentCode))
            return false;

        var derivedParentCode = DeriveParentCode(childCode);
        return string.Equals(derivedParentCode, parentCode, StringComparison.OrdinalIgnoreCase);
    }
}