using Docman.Core.Application.Common.DTOs.Buildings;
using Docman.Core.Application.Features.Buildings.Commands.CreateBuilding;
using Docman.Core.Application.Features.Buildings.Commands.UpdateBuilding;
using Docman.Core.Application.Features.Buildings.Queries.GetAllBuildings;
using Docman.Core.Application.Features.Buildings.Queries.GetBuildingById;


namespace Docman.Presentation.API.Controllers;

/// <summary>
/// Manage building records - view, create, and update building information.
/// </summary>
/// <remarks>
/// 🔐 All endpoints require authentication (Bearer token).
/// 🏢 **Perfect for:** Building management systems, property listings, facility management.
/// 📍 Each building has location details (country, city, district) and approval status.
/// </remarks>
[SwaggerResponse(200, "Operation completed successfully")]
[SwaggerResponse(400, "Invalid request data")]
[SwaggerResponse(401, "User is not authorized")]
[SwaggerResponse(404, "Building not found")]
[Authorize] // Require authorization for all endpoints as specified in requirements
public class BuildingsController(IMediator mediator) : BaseController
{
    /// <summary>
    /// Get list of all buildings with basic information.
    /// </summary>
    /// <returns>
    /// List of buildings with name, location, status and basic details.
    /// </returns>
    [HttpGet, SwaggerOperation(
        Summary = "Get all buildings",
        Description = "**Get complete list of all buildings in the system.**<br/><br/>📋 **What you get:**<br/>• Building names and descriptions<br/>• Location details (country, city, district)<br/>• Approval status (Approved, Unapproved, Rejected)<br/>• Creation dates<br/><br/>🎯 **Use this to:** Build building lists, property dashboards, management interfaces, search filters")]
    [ProducesResponseType(typeof(IEnumerable<BuildingListDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetAllBuildings()
    {
        var query = new GetAllBuildingsQuery();
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Get detailed information about a specific building.
    /// </summary>
    /// <param name="id">The unique ID number of the building</param>
    /// <returns>
    /// Complete building details including location, status, and audit information.
    /// </returns>
    [HttpGet("{id:int}"), SwaggerOperation(
        Summary = "Get building details",
        Description = "**Get complete information about a specific building.**<br/><br/>🏢 **Detailed info includes:**<br/>• Full building details (name, description)<br/>• Complete location information<br/>• Current approval status<br/>• Creation and modification dates<br/>• User who created/modified<br/><br/>💡 **Example:** GET `/123` - Get details for building with ID 123<br/><br/>🎯 **Use this to:** Building detail pages, edit forms, audit trails, property information")]
    [ProducesResponseType(typeof(BuildingDetailsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetBuildingById(int id)
    {
        var query = new GetBuildingByIdQuery(id);
        var result = await mediator.Send(query);

        return result.ToActionResult();
    }

    /// <summary>
    /// Add a new building to the system.
    /// </summary>
    /// <param name="command">Building information (name, description, location details)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// ID of the newly created building.
    /// </returns>
    [HttpPost, SwaggerOperation(
        Summary = "Create new building",
        Description = "**Add a new building to the system.**<br/><br/>📝 **Required information:**<br/>• Building name<br/>• Description<br/>• Location details (country, governorate, city, district)<br/>• Optional: Additional building details<br/><br/>✨ **What happens:**<br/>• Building is created with 'Unapproved' status<br/>• System assigns unique ID<br/>• Tracks who created it and when<br/><br/>🎯 **Use this to:** Add property forms, building registration, facility management")]
    [ProducesResponseType(typeof(int), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateBuilding([FromBody] CreateBuildingCommand command,
        CancellationToken cancellationToken)
    {
        var result = await mediator.Send(command, cancellationToken);

        if (result.IsSuccess)
        {
            return CreatedAtAction(
                nameof(GetBuildingById),
                new { id = result.Value },
                new { Id = result.Value }
            );
        }

        return result.ToActionResult();
    }

    /// <summary>
    /// Update existing building information.
    /// </summary>
    /// <param name="id">The unique ID of the building to update</param>
    /// <param name="command">Updated building information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// Success confirmation that building was updated.
    /// </returns>
    /// <remarks>
    /// ⚠️ **Important:** Status resets to 'Unapproved' when building data is changed (unless explicitly set).
    /// </remarks>
    [HttpPut("{id:int}"), SwaggerOperation(
        Summary = "Update building information",
        Description = "**Update an existing building's information.**<br/><br/>✏️ **What you can update:**<br/>• Building name and description<br/>• Location details<br/>• Status (Approved, Unapproved, Rejected)<br/>• Other building properties<br/><br/>⚠️ **Status behavior:**<br/>• If you change building data, status resets to 'Unapproved'<br/>• Unless you explicitly set a different status<br/>• This ensures modified buildings get re-approved<br/><br/>🎯 **Use this to:** Edit building forms, approve/reject buildings, update property information")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateBuilding(
        int id,
        [FromBody] UpdateBuildingCommand command,
        CancellationToken cancellationToken)
    {
        var result = await mediator.Send(command, cancellationToken);

        return result.ToActionResult();
    }
}