namespace Docman.Core.Application.Common.DTOs.OrganizationalStructure;

/// <summary>
/// Request model for updating an existing organizational node
/// Note: Code cannot be changed after creation to maintain hierarchy integrity
/// </summary>
public class UpdateOrganizationalNodeRequest
{
    /// <summary>
    /// Updated display name of the organizational node
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Updated description of the organizational node
    /// </summary>
    public string? Description { get; set; }


}
