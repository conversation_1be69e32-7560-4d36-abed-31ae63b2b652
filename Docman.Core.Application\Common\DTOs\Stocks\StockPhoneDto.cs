namespace Docman.Core.Application.Common.DTOs.Stocks;

/// <summary>
/// DTO for stock phone information
/// </summary>
public sealed record StockPhoneDto
{
    /// <summary>
    /// Unique identifier of the stock phone
    /// </summary>
    public int Id { get; init; }

    /// <summary>
    /// The contact number
    /// </summary>
    public string PhoneNumber { get; init; } = string.Empty;

    /// <summary>
    /// Additional notes about the number
    /// </summary>
    public string? Notes { get; init; }
}
