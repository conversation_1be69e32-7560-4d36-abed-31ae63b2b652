using Docman.Core.Application.Common.Errors;

namespace Docman.Core.Application.Features.Locations.Queries.GetDistrictsByCityQuery;

public class GetDistrictsByCityQueryHandler(
    IGenericRepository<CityOrDistrict> cityRepository,
    IGenericRepository<DistrictOrVillage> districtRepository)
    : IRequestHandler<GetDistrictsByCityQuery, Result<GetDistrictsByCityResponse>>
{
    public async Task<Result<GetDistrictsByCityResponse>> Handle(GetDistrictsByCityQuery request, CancellationToken cancellationToken)
    {
        // Check if city exists
        var cityExists = await cityRepository.AnyAsync(
            c => c.Code == request.CityCode,
            cancellationToken);

        if (!cityExists)
        {
            return Result.Failure<GetDistrictsByCityResponse>(LocationErrors.CityNotFound);
        }

        var districts = await districtRepository.GetAllAsync(
            filter: d => d.CityOrDistrictCode == request.CityCode,
            tracked: false,
            cancellationToken: cancellationToken);

        var districtDtos = districts.Adapt<List<DistrictDto>>();
        
        return Result.Success(new GetDistrictsByCityResponse(districtDtos));
    }
}
