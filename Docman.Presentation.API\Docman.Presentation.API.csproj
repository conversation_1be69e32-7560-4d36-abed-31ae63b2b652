<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>e14852c1-4842-4444-bb5d-43e8e0a1dd78</UserSecretsId>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Docman.Core.Application\Docman.Core.Application.csproj" />
    <ProjectReference Include="..\Docman.Infrastructure.Persistence\Docman.Infrastructure.Persistence.csproj" />
    <ProjectReference Include="..\Docman.Infrastructure.Integration\Docman.Infrastructure.Integration.csproj" />
  </ItemGroup>

</Project>
