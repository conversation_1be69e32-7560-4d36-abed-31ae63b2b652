using Docman.Core.Domain.Entities;

namespace Docman.Core.Domain.Common;

/// <summary>
/// Base class for entities that require comprehensive audit logging with user tracking
/// </summary>
public abstract class AuditableEntity
{
    /// <summary>
    /// User ID who created this entity
    /// </summary>
    public string? CreatedByID { get; set; }
    
    /// <summary>
    /// Timestamp when this entity was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
    
    /// <summary>
    /// User ID who last updated this entity
    /// </summary>
    public string? UpdatedByID { get; set; }
    
    /// <summary>
    /// Timestamp when this entity was last updated
    /// </summary>
    public DateTimeOffset? UpdatedAt { get; set; }
    
    /// <summary>
    /// Indicates if this entity is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;
    
    /// <summary>
    /// Timestamp when this entity was soft deleted
    /// </summary>
    public DateTimeOffset? DeletedAt { get; set; }
    
    /// <summary>
    /// User ID who soft deleted this entity
    /// </summary>
    public string? DeletedByID { get; set; }

    // Navigation properties for user tracking
    /// <summary>
    /// Navigation property to the user who created this entity
    /// </summary>
    public ApplicationUser? CreatedBy { get; set; }
    
    /// <summary>
    /// Navigation property to the user who last updated this entity
    /// </summary>
    public ApplicationUser? UpdatedBy { get; set; }
    
    /// <summary>
    /// Navigation property to the user who soft deleted this entity
    /// </summary>
    public ApplicationUser? DeletedBy { get; set; }
}

/// <summary>
/// Generic auditable entity with strongly-typed primary key
/// </summary>
/// <typeparam name="TKey">The type of the primary key</typeparam>
public abstract class AuditableEntity<TKey> : AuditableEntity
{
    /// <summary>
    /// Primary key of the entity
    /// </summary>
    public TKey Id { get; set; } = default!;
}
