namespace Docman.Core.Application.Common.DTOs.OrganizationalStructure;

/// <summary>
/// Data transfer object for organizational node information
/// </summary>
public class OrganizationalNodeDto
{
    /// <summary>
    /// 5-character Base-36 code
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Display name of the node
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Organizational level (L1-L4)
    /// </summary>
    public OrganizationalLevel Level { get; set; }

    /// <summary>
    /// Parent code (null for L1)
    /// </summary>
    public string? ParentCode { get; set; }


}
