﻿using Docman.Core.Application.Common.DTOs;
using Docman.Core.Application.Common.DTOs.Auth;
using Docman.Core.Application.Features.Authentication.Login;
using Docman.Core.Application.Features.Authentication.RefreshToken;

namespace Docman.Presentation.API.Controllers;

/// <summary>
/// Handle user authentication - login, logout, and token refresh.
/// </summary>
/// <remarks>
/// 🔑 **Authentication endpoints** - no Bearer token required for these endpoints.
/// 💡 **Perfect for:** Login forms, automatic token refresh, user logout functionality.
/// </remarks>
[SwaggerResponse(200, "Operation completed successfully")]
[SwaggerResponse(400, "Invalid credentials or request data")]
[SwaggerResponse(401, "Authentication failed")]
public class AuthController(IMediator mediator) : BaseController
{
    private readonly IMediator _mediator = mediator;

    /// <summary>
    /// Login user with email and password to get access tokens.
    /// </summary>
    /// <param name="request">User login credentials (email and password)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// Access token and refresh token for authenticated requests, plus user information.
    /// </returns>
    [HttpPost, SwaggerOperation(
         Summary = "User login",
         Description =
             "**Login user and get access tokens.**<br/><br/>📝 **Required:**<br/>• Valid email address<br/>• Correct password<br/><br/>✅ **Returns:**<br/>• Access token (use in Authorization header)<br/>• Refresh token (use to get new access tokens)<br/>• User information<br/><br/>🎯 **Use this to:** Build login forms, authenticate users, get initial tokens")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Login([FromBody] LoginRequest request, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new LoginCommand(request.Email, request.Password), cancellationToken);
        return result.IsSuccess ? Ok(result.Value) : BadRequest(result.Error);
    }

    /// <summary>
    /// Get new access token using refresh token (when current token expires).
    /// </summary>
    /// <param name="request">Current access token and refresh token</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>
    /// New access token and refresh token pair.
    /// </returns>
    [HttpPost("refresh-token"), SwaggerOperation(
         Summary = "Refresh access token",
         Description =
             "**Get new access token when the current one expires.**<br/><br/>🔄 **How it works:**<br/>• Send your expired access token + refresh token<br/>• Get back new access token + new refresh token<br/>• Continue using the app without re-login<br/><br/>⏰ **When to use:**<br/>• Access token expires (usually 15-60 minutes)<br/>• API returns 401 Unauthorized<br/>• Before token expires (proactive refresh)<br/><br/>🎯 **Use this to:** Auto-refresh tokens, maintain user sessions, prevent login interruptions")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request,
        CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new RefreshTokenCommand(request.Token, request.RefreshToken),
            cancellationToken);
        return result.IsSuccess ? Ok(result.Value) : BadRequest(result.Error);
    }

    /// <summary>
    /// Logout user by invalidating their tokens (revoke access).
    /// </summary>
    /// <param name="request">Current access token and refresh token to invalidate</param>
    /// <returns>
    /// Success confirmation that user has been logged out.
    /// </returns>
    [HttpPost("revoke-token"), SwaggerOperation(
         Summary = "Logout user (revoke tokens)",
         Description =
             "**Logout user by invalidating their access and refresh tokens.**<br/><br/>🚪 **What happens:**<br/>• Current access token becomes invalid<br/>• Refresh token becomes invalid<br/>• User needs to login again<br/>• Ensures secure logout<br/><br/>🔒 **Security benefit:**<br/>• Prevents token misuse if device is lost/stolen<br/>• Clean logout from all sessions<br/><br/>🎯 **Use this to:** Logout buttons, security features, session management")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> RevokeToken([FromBody] RefreshTokenRequest request)
    {
        var result = await _mediator.Send(new RevokeRefreshTokenCommand(request.Token, request.RefreshToken));
        return result.IsSuccess ? Ok() : BadRequest(result.Error);
    }
}