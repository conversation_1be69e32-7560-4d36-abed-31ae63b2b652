using Docman.Core.Domain.Enums;

namespace Docman.Core.Application.Common.DTOs.DocumentTypes;

/// <summary>
/// Request model for updating an existing document type
/// </summary>
public class UpdateDocumentTypeRequest
{
    /// <summary>
    /// New name for the document type (required)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// New status for the document type (required)
    /// </summary>
    public DocumentTypeStatus Status { get; set; }
}

