using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Queries.GetOrganizationalNodeByCode;

/// <summary>
/// Handler for retrieving organizational node details by code
/// </summary>
public sealed class GetOrganizationalNodeByCodeQueryHandler(
    IOrganizationalNodeRepository organizationalNodeRepository
) : IRequestHandler<GetOrganizationalNodeByCodeQuery, Result<OrganizationalNodeDetailsDto>>
{
    public async Task<Result<OrganizationalNodeDetailsDto>> Handle(
        GetOrganizationalNodeByCodeQuery request,
        CancellationToken cancellationToken)
    {
        // Get the node
        var node = await organizationalNodeRepository.GetByCodeAsync(
            request.Code,
            includeChildren: request.IncludeChildren,
            cancellationToken);

        if (node is null)
            return Result.Failure<OrganizationalNodeDetailsDto>(OrganizationalNodeErrors.NodeNotFound);


        // Get parent if exists
        OrganizationalNode? parent = null;
        if (node.ParentCode != null)
        {
            parent = await organizationalNodeRepository.GetByCodeAsync(
                node.ParentCode,
                cancellationToken: cancellationToken);
        }

        // Get children if requested
        List<OrganizationalNode> children = new();
        if (request.IncludeChildren)
        {
            children = await organizationalNodeRepository.GetChildrenAsync(
                request.Code,
                cancellationToken);
        }

        // Get descendants count
        var allDescendants = await organizationalNodeRepository.GetDescendantsAsync(
            request.Code,
            cancellationToken);

        // Map to DTO
        var result = node.Adapt<OrganizationalNodeDetailsDto>();
        result.Parent = parent?.Adapt<OrganizationalNodeDto>();
        result.Children = children.Adapt<List<OrganizationalNodeDto>>();
        result.PathToRoot = OrganizationalCodeHelper.GetPathToRoot(node.Code);
        result.ChildrenCount = children.Count;
        result.DescendantsCount = allDescendants.Count;

        return Result.Success(result);
    }
}