using Docman.Core.Application.Contracts.Specifications;

namespace Docman.Core.Application.Features.OrganizationalStructure.Queries.GetNodesByLevel;

/// <summary>
/// Handler for retrieving nodes by organizational level
/// </summary>
public sealed class GetNodesByLevelQueryHandler(
    IOrganizationalNodeRepository organizationalNodeRepository
) : IRequestHandler<GetNodesByLevelQuery, Result<List<OrganizationalNodeDto>>>
{
    public async Task<Result<List<OrganizationalNodeDto>>> Handle(
        GetNodesByLevelQuery request,
        CancellationToken cancellationToken)
    {
        var nodes = await organizationalNodeRepository.GetNodesByLevelAsync(
            request.Level,
            cancellationToken);

        var result = nodes
            .OrderBy(n => n.Code)
            .Adapt<List<OrganizationalNodeDto>>();

        return Result.Success(result);
    }
}
